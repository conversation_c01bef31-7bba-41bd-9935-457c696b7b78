"""
电脑端蓝牙BLE客户端
用于扫描、连接设备并发送控制命令
"""

import asyncio
import logging
from typing import Optional, List, Callable, Dict, Any
import sys
import os
from bleak import BleakClient, BleakScanner
from bleak.backends.device import BLEDevice

# 添加common目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from common.protocol import (
    AP_CONTROL_SERVICE_UUID,
    COMMAND_CHARACTERISTIC_UUID,
    STATUS_CHARACTERISTIC_UUID,
    AP_INFO_CHARACTERISTIC_UUID,
    Protocol,
    CommandType,
    StatusType,
    APInfo
)

logger = logging.getLogger(__name__)

class BLEClient:
    """BLE客户端类"""
    
    def __init__(self):
        self.client: Optional[BleakClient] = None
        self.connected_device: Optional[BLEDevice] = None
        self.is_connected = False
        
        # 回调函数
        self.on_status_changed: Optional[Callable] = None
        self.on_ap_info_received: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None
        
        # 特征值UUID
        self.command_char_uuid = COMMAND_CHARACTERISTIC_UUID
        self.status_char_uuid = STATUS_CHARACTERISTIC_UUID
        self.ap_info_char_uuid = AP_INFO_CHARACTERISTIC_UUID
    
    async def scan_devices(self, timeout: float = 10.0) -> List[BLEDevice]:
        """扫描BLE设备"""
        logger.info(f"开始扫描BLE设备，超时时间: {timeout}秒")
        
        try:
            devices = await BleakScanner.discover(timeout=timeout)
            
            # 过滤包含目标服务的设备
            target_devices = []
            for device in devices:
                if device.name and "RK3588" in device.name:
                    target_devices.append(device)
                    logger.info(f"发现目标设备: {device.name} ({device.address})")
            
            logger.info(f"扫描完成，发现 {len(target_devices)} 个目标设备")
            return target_devices
            
        except Exception as e:
            logger.error(f"扫描设备失败: {e}")
            return []
    
    async def connect(self, device: BLEDevice) -> bool:
        """连接到指定设备"""
        if self.is_connected:
            logger.warning("已连接到设备，请先断开连接")
            return False
        
        try:
            logger.info(f"正在连接设备: {device.name} ({device.address})")
            
            self.client = BleakClient(device.address)
            await self.client.connect()
            
            # 检查服务是否存在
            services = await self.client.get_services()
            service_found = False
            
            for service in services:
                if service.uuid.lower() == AP_CONTROL_SERVICE_UUID.lower():
                    service_found = True
                    logger.info(f"找到目标服务: {service.uuid}")
                    break
            
            if not service_found:
                logger.error("设备不包含目标服务")
                await self.client.disconnect()
                return False
            
            # 设置通知回调
            await self._setup_notifications()
            
            self.connected_device = device
            self.is_connected = True
            
            logger.info(f"成功连接到设备: {device.name}")
            return True
            
        except Exception as e:
            logger.error(f"连接设备失败: {e}")
            if self.client:
                try:
                    await self.client.disconnect()
                except:
                    pass
                self.client = None
            return False
    
    async def _setup_notifications(self):
        """设置通知回调"""
        try:
            # 状态通知
            await self.client.start_notify(
                self.status_char_uuid,
                self._on_status_notification
            )
            
            # AP信息通知
            await self.client.start_notify(
                self.ap_info_char_uuid,
                self._on_ap_info_notification
            )
            
            logger.info("通知回调设置完成")
            
        except Exception as e:
            logger.error(f"设置通知回调失败: {e}")
    
    async def _on_status_notification(self, sender, data: bytes):
        """处理状态通知"""
        try:
            status, message = Protocol.decode_status(data)
            logger.info(f"收到状态更新: {status.value}, 消息: {message}")
            
            if self.on_status_changed:
                await self.on_status_changed(status, message)
                
        except Exception as e:
            logger.error(f"处理状态通知失败: {e}")
    
    async def _on_ap_info_notification(self, sender, data: bytes):
        """处理AP信息通知"""
        try:
            ap_info = Protocol.decode_ap_info(data)
            logger.info(f"收到AP信息: SSID={ap_info.ssid}, IP={ap_info.ip}")
            
            if self.on_ap_info_received:
                await self.on_ap_info_received(ap_info)
                
        except Exception as e:
            logger.error(f"处理AP信息通知失败: {e}")
    
    async def send_command(self, cmd_type: CommandType, **params) -> bool:
        """发送命令到设备"""
        if not self.is_connected or not self.client:
            logger.error("设备未连接")
            return False
        
        try:
            data = Protocol.encode_command(cmd_type, **params)
            await self.client.write_gatt_char(self.command_char_uuid, data)
            
            logger.info(f"发送命令: {cmd_type.value}, 参数: {params}")
            return True
            
        except Exception as e:
            logger.error(f"发送命令失败: {e}")
            return False
    
    async def start_ap(self, ssid: str = None, password: str = None) -> bool:
        """启动AP"""
        params = {}
        if ssid:
            params['ssid'] = ssid
        if password:
            params['password'] = password
            
        return await self.send_command(CommandType.START_AP, **params)
    
    async def stop_ap(self) -> bool:
        """停止AP"""
        return await self.send_command(CommandType.STOP_AP)
    
    async def get_status(self) -> bool:
        """获取状态"""
        return await self.send_command(CommandType.GET_STATUS)
    
    async def get_ap_info(self) -> bool:
        """获取AP信息"""
        return await self.send_command(CommandType.GET_AP_INFO)
    
    async def read_status(self) -> Optional[tuple]:
        """读取当前状态"""
        if not self.is_connected or not self.client:
            return None
        
        try:
            data = await self.client.read_gatt_char(self.status_char_uuid)
            return Protocol.decode_status(data)
        except Exception as e:
            logger.error(f"读取状态失败: {e}")
            return None
    
    async def read_ap_info(self) -> Optional[APInfo]:
        """读取AP信息"""
        if not self.is_connected or not self.client:
            return None
        
        try:
            data = await self.client.read_gatt_char(self.ap_info_char_uuid)
            if data:
                return Protocol.decode_ap_info(data)
            return None
        except Exception as e:
            logger.error(f"读取AP信息失败: {e}")
            return None
    
    async def disconnect(self):
        """断开连接"""
        if not self.is_connected:
            return
        
        try:
            if self.client:
                # 停止通知
                try:
                    await self.client.stop_notify(self.status_char_uuid)
                    await self.client.stop_notify(self.ap_info_char_uuid)
                except:
                    pass
                
                # 断开连接
                await self.client.disconnect()
                self.client = None
            
            self.connected_device = None
            self.is_connected = False
            
            logger.info("已断开设备连接")
            
            if self.on_disconnected:
                await self.on_disconnected()
                
        except Exception as e:
            logger.error(f"断开连接失败: {e}")
    
    def set_status_callback(self, callback: Callable):
        """设置状态变化回调"""
        self.on_status_changed = callback
    
    def set_ap_info_callback(self, callback: Callable):
        """设置AP信息接收回调"""
        self.on_ap_info_received = callback
    
    def set_disconnect_callback(self, callback: Callable):
        """设置断开连接回调"""
        self.on_disconnected = callback
    
    def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        return {
            "connected": self.is_connected,
            "device_name": self.connected_device.name if self.connected_device else None,
            "device_address": self.connected_device.address if self.connected_device else None
        }

# 测试代码
async def test_ble_client():
    """测试BLE客户端"""
    logging.basicConfig(level=logging.INFO)
    
    client = BLEClient()
    
    async def on_status_changed(status: StatusType, message: str):
        print(f"状态变化: {status.value} - {message}")
    
    async def on_ap_info_received(ap_info: APInfo):
        print(f"收到AP信息: SSID={ap_info.ssid}, 密码={ap_info.password}, IP={ap_info.ip}")
    
    client.set_status_callback(on_status_changed)
    client.set_ap_info_callback(on_ap_info_received)
    
    try:
        # 扫描设备
        print("扫描设备中...")
        devices = await client.scan_devices(10)
        
        if not devices:
            print("未发现目标设备")
            return
        
        # 连接第一个设备
        device = devices[0]
        print(f"连接设备: {device.name}")
        
        if await client.connect(device):
            print("连接成功！")
            
            # 测试命令
            await asyncio.sleep(2)
            await client.start_ap("TestAP", "12345678")
            
            await asyncio.sleep(5)
            await client.get_ap_info()
            
            await asyncio.sleep(5)
            await client.stop_ap()
            
            # 保持连接一段时间
            await asyncio.sleep(10)
        else:
            print("连接失败")
            
    except KeyboardInterrupt:
        print("正在断开连接...")
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(test_ble_client())
