# 蓝牙AP控制系统

通过蓝牙控制设备打开WiFi AP，并自动连接的系统。

## 架构设计

### 设备端 (Device)
- 运行蓝牙BLE服务器
- 接收控制命令
- 管理WiFi AP热点
- 广播AP信息

### 电脑端 (Computer)
- 蓝牙BLE客户端
- 发送控制命令
- 自动连接AP
- 管理WiFi连接

## 功能特性

1. **蓝牙通信**
   - 设备发现
   - 命令传输
   - AP信息同步

2. **WiFi管理**
   - AP创建/关闭
   - 自动连接
   - 连接状态监控

3. **自动化流程**
   - 一键控制
   - 状态反馈
   - 错误处理

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 设备端
```bash
python device/main.py
```

### 电脑端
```bash
python computer/main.py
```

## 项目结构

```
bluetooth_ap/
├── device/           # 设备端代码
│   ├── main.py      # 主程序
│   ├── ble_server.py # 蓝牙服务器
│   └── wifi_manager.py # WiFi管理
├── computer/         # 电脑端代码
│   ├── main.py      # 主程序
│   ├── ble_client.py # 蓝牙客户端
│   └── wifi_manager.py # WiFi管理
├── common/           # 共享代码
│   ├── protocol.py  # 通信协议
│   └── utils.py     # 工具函数
└── requirements.txt  # 依赖文件
``` 