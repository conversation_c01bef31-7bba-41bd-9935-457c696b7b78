#!/usr/bin/env python3
"""
电脑端启动脚本
提供多种启动模式：命令行、Web界面、自动模式
"""

import sys
import os
import argparse
import asyncio
import threading
import webbrowser
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def start_web_interface(host='0.0.0.0', port=5000, auto_open=True):
    """启动Web界面"""
    print(f"🌐 启动Web界面...")
    print(f"   地址: http://{host}:{port}")
    
    # 导入并启动Flask应用
    from web_app import app, socketio
    
    if auto_open:
        # 延迟打开浏览器
        def open_browser():
            time.sleep(2)
            webbrowser.open(f'http://localhost:{port}')
        
        threading.Thread(target=open_browser, daemon=True).start()
    
    try:
        socketio.run(app, host=host, port=port, debug=False)
    except KeyboardInterrupt:
        print("\n🛑 Web界面已停止")

def start_cli_mode():
    """启动命令行模式"""
    print("🖥️  启动命令行模式...")
    
    from computer.main import cli
    
    try:
        cli()
    except KeyboardInterrupt:
        print("\n🛑 命令行模式已停止")

def start_auto_mode(ssid=None, password=None, no_wifi=False):
    """启动自动模式"""
    print("🤖 启动自动模式...")
    
    from computer.main import _auto_mode
    
    try:
        asyncio.run(_auto_mode(ssid, password, not no_wifi))
    except KeyboardInterrupt:
        print("\n🛑 自动模式已停止")

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_packages = [
        'flask', 'flask-socketio', 'bleak', 'click', 'rich'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少以下依赖包: {', '.join(missing_packages)}")
        print(f"💡 请运行: pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 依赖检查通过")
    return True

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖...")
    
    requirements_file = project_root / 'requirements.txt'
    
    if requirements_file.exists():
        import subprocess
        try:
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)
            ], check=True)
            print("✅ 依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖安装失败: {e}")
            return False
    else:
        print("❌ requirements.txt 文件不存在")
        return False

def show_system_info():
    """显示系统信息"""
    import platform
    
    print("📋 系统信息:")
    print(f"   操作系统: {platform.system()} {platform.release()}")
    print(f"   Python版本: {platform.python_version()}")
    print(f"   架构: {platform.machine()}")
    
    # 检查蓝牙支持
    try:
        import bleak
        print(f"   蓝牙库: bleak {bleak.__version__}")
    except ImportError:
        print("   蓝牙库: 未安装")
    
    # 检查WiFi支持
    if platform.system() == "Windows":
        try:
            import pywifi
            print("   WiFi库: pywifi (Windows)")
        except ImportError:
            print("   WiFi库: netsh (Windows)")
    elif platform.system() == "Linux":
        print("   WiFi库: nmcli (Linux)")
    else:
        print("   WiFi库: 不支持")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='蓝牙AP控制系统 - 电脑端',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s web                    # 启动Web界面
  %(prog)s cli                    # 启动命令行模式
  %(prog)s auto                   # 启动自动模式
  %(prog)s auto --ssid MyAP       # 自动模式指定AP名称
  %(prog)s install                # 安装依赖
  %(prog)s info                   # 显示系统信息
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # Web界面模式
    web_parser = subparsers.add_parser('web', help='启动Web界面')
    web_parser.add_argument('--host', default='0.0.0.0', help='绑定地址 (默认: 0.0.0.0)')
    web_parser.add_argument('--port', type=int, default=5000, help='端口号 (默认: 5000)')
    web_parser.add_argument('--no-browser', action='store_true', help='不自动打开浏览器')
    
    # 命令行模式
    cli_parser = subparsers.add_parser('cli', help='启动命令行模式')
    
    # 自动模式
    auto_parser = subparsers.add_parser('auto', help='启动自动模式')
    auto_parser.add_argument('--ssid', help='AP名称')
    auto_parser.add_argument('--password', help='AP密码')
    auto_parser.add_argument('--no-wifi', action='store_true', help='不自动连接WiFi')
    
    # 工具命令
    subparsers.add_parser('install', help='安装依赖')
    subparsers.add_parser('check', help='检查依赖')
    subparsers.add_parser('info', help='显示系统信息')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    print("🔗 蓝牙AP控制系统 - 电脑端")
    print("=" * 50)
    
    if args.command == 'web':
        if not check_dependencies():
            return
        start_web_interface(args.host, args.port, not args.no_browser)
        
    elif args.command == 'cli':
        if not check_dependencies():
            return
        start_cli_mode()
        
    elif args.command == 'auto':
        if not check_dependencies():
            return
        start_auto_mode(args.ssid, args.password, args.no_wifi)
        
    elif args.command == 'install':
        install_dependencies()
        
    elif args.command == 'check':
        check_dependencies()
        
    elif args.command == 'info':
        show_system_info()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        sys.exit(1)
