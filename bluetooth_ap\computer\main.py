"""
电脑端主程序
整合BLE客户端和WiFi管理器，实现完整的蓝牙AP控制流程
"""

import asyncio
import logging
import sys
import os
from typing import Optional, List
import json
from datetime import datetime
import click
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Prompt, Confirm

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from ble_client import BLEClient
from wifi_manager import WiFiManager
from common.protocol import APInfo, StatusType

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)
console = Console()

class ComputerController:
    """电脑端控制器"""
    
    def __init__(self):
        self.ble_client = BLEClient()
        self.wifi_manager = WiFiManager()
        
        # 状态
        self.current_ap_info: Optional[APInfo] = None
        self.auto_connect_wifi = True
        
        # 设置回调
        self._setup_callbacks()
    
    def _setup_callbacks(self):
        """设置回调函数"""
        self.ble_client.set_status_callback(self._handle_status_changed)
        self.ble_client.set_ap_info_callback(self._handle_ap_info_received)
        self.ble_client.set_disconnect_callback(self._handle_ble_disconnected)
        
        self.wifi_manager.set_connect_callback(self._handle_wifi_connected)
        self.wifi_manager.set_disconnect_callback(self._handle_wifi_disconnected)
        self.wifi_manager.set_connection_failed_callback(self._handle_wifi_connection_failed)
    
    async def _handle_status_changed(self, status: StatusType, message: str):
        """处理设备状态变化"""
        console.print(f"[blue]设备状态更新:[/blue] {status.value} - {message}")
        
        if status == StatusType.AP_RUNNING:
            console.print("[green]✓[/green] 设备AP已启动")
        elif status == StatusType.AP_STOPPED:
            console.print("[yellow]⚠[/yellow] 设备AP已停止")
        elif status == StatusType.ERROR:
            console.print(f"[red]✗[/red] 设备错误: {message}")
    
    async def _handle_ap_info_received(self, ap_info: APInfo):
        """处理接收到的AP信息"""
        self.current_ap_info = ap_info
        console.print(f"[green]✓[/green] 收到AP信息:")
        console.print(f"  SSID: {ap_info.ssid}")
        console.print(f"  密码: {ap_info.password}")
        console.print(f"  IP: {ap_info.ip}")
        
        # 自动连接WiFi
        if self.auto_connect_wifi:
            console.print("[blue]正在自动连接到设备AP...[/blue]")
            await self.connect_to_device_ap()
    
    async def _handle_ble_disconnected(self):
        """处理BLE断开连接"""
        console.print("[red]⚠[/red] BLE连接已断开")
    
    async def _handle_wifi_connected(self, ap_info: APInfo):
        """处理WiFi连接成功"""
        console.print(f"[green]✓[/green] 成功连接到设备AP: {ap_info.ssid}")
        console.print(f"[green]✓[/green] 现在可以通过 {ap_info.ip} 访问设备")
    
    async def _handle_wifi_disconnected(self, connection_info: dict):
        """处理WiFi断开连接"""
        console.print(f"[yellow]⚠[/yellow] WiFi连接已断开: {connection_info['ssid']}")
    
    async def _handle_wifi_connection_failed(self, ap_info: APInfo):
        """处理WiFi连接失败"""
        console.print(f"[red]✗[/red] 连接设备AP失败: {ap_info.ssid}")
    
    async def scan_and_connect_device(self) -> bool:
        """扫描并连接设备"""
        console.print("[blue]正在扫描蓝牙设备...[/blue]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("扫描中...", total=None)
            devices = await self.ble_client.scan_devices(10)
        
        if not devices:
            console.print("[red]✗[/red] 未发现目标设备")
            return False
        
        # 显示发现的设备
        table = Table(title="发现的设备")
        table.add_column("序号", style="cyan")
        table.add_column("设备名称", style="green")
        table.add_column("地址", style="yellow")
        
        for i, device in enumerate(devices):
            table.add_row(str(i + 1), device.name or "未知", device.address)
        
        console.print(table)
        
        # 选择设备
        if len(devices) == 1:
            selected_device = devices[0]
            console.print(f"[blue]自动选择唯一设备:[/blue] {selected_device.name}")
        else:
            choice = Prompt.ask(
                "请选择要连接的设备",
                choices=[str(i + 1) for i in range(len(devices))],
                default="1"
            )
            selected_device = devices[int(choice) - 1]
        
        # 连接设备
        console.print(f"[blue]正在连接设备:[/blue] {selected_device.name}")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("连接中...", total=None)
            success = await self.ble_client.connect(selected_device)
        
        if success:
            console.print(f"[green]✓[/green] 成功连接到设备: {selected_device.name}")
            return True
        else:
            console.print(f"[red]✗[/red] 连接设备失败: {selected_device.name}")
            return False
    
    async def start_device_ap(self, ssid: str = None, password: str = None) -> bool:
        """启动设备AP"""
        if not self.ble_client.is_connected:
            console.print("[red]✗[/red] 设备未连接")
            return False
        
        console.print("[blue]正在启动设备AP...[/blue]")
        
        success = await self.ble_client.start_ap(ssid, password)
        if success:
            console.print("[green]✓[/green] AP启动命令已发送")
            return True
        else:
            console.print("[red]✗[/red] 发送AP启动命令失败")
            return False
    
    async def stop_device_ap(self) -> bool:
        """停止设备AP"""
        if not self.ble_client.is_connected:
            console.print("[red]✗[/red] 设备未连接")
            return False
        
        console.print("[blue]正在停止设备AP...[/blue]")
        
        success = await self.ble_client.stop_ap()
        if success:
            console.print("[green]✓[/green] AP停止命令已发送")
            return True
        else:
            console.print("[red]✗[/red] 发送AP停止命令失败")
            return False
    
    async def connect_to_device_ap(self) -> bool:
        """连接到设备AP"""
        if not self.current_ap_info:
            console.print("[red]✗[/red] 没有AP信息")
            return False
        
        console.print(f"[blue]正在连接到设备AP:[/blue] {self.current_ap_info.ssid}")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("连接中...", total=None)
            success = await self.wifi_manager.connect_to_ap(self.current_ap_info)
        
        return success
    
    async def disconnect_wifi(self) -> bool:
        """断开WiFi连接"""
        return await self.wifi_manager.disconnect()
    
    async def get_device_status(self) -> bool:
        """获取设备状态"""
        if not self.ble_client.is_connected:
            console.print("[red]✗[/red] 设备未连接")
            return False
        
        return await self.ble_client.get_status()
    
    def get_connection_info(self) -> dict:
        """获取连接信息"""
        return {
            "ble": self.ble_client.get_connection_info(),
            "wifi": self.wifi_manager.get_connection_status(),
            "ap_info": self.current_ap_info.to_dict() if self.current_ap_info else None,
            "timestamp": datetime.now().isoformat()
        }
    
    async def cleanup(self):
        """清理资源"""
        console.print("[blue]正在清理资源...[/blue]")
        
        # 断开WiFi
        if self.wifi_manager.is_connected:
            await self.wifi_manager.disconnect()
        
        # 断开BLE
        if self.ble_client.is_connected:
            await self.ble_client.disconnect()
        
        console.print("[green]✓[/green] 资源清理完成")

# CLI命令
@click.group()
def cli():
    """蓝牙AP控制系统 - 电脑端"""
    pass

@cli.command()
@click.option('--ssid', help='AP的SSID名称')
@click.option('--password', help='AP的密码')
@click.option('--no-auto-wifi', is_flag=True, help='不自动连接WiFi')
def auto():
    """自动模式：扫描设备、连接、启动AP并自动连接WiFi"""
    asyncio.run(_auto_mode(ssid, password, not no_auto_wifi))

@cli.command()
def interactive():
    """交互模式"""
    asyncio.run(_interactive_mode())

@cli.command()
def scan():
    """仅扫描设备"""
    asyncio.run(_scan_only())

async def _auto_mode(ssid: str, password: str, auto_wifi: bool):
    """自动模式实现"""
    controller = ComputerController()
    controller.auto_connect_wifi = auto_wifi
    
    try:
        # 1. 扫描并连接设备
        if not await controller.scan_and_connect_device():
            return
        
        # 等待连接稳定
        await asyncio.sleep(2)
        
        # 2. 启动设备AP
        if not await controller.start_device_ap(ssid, password):
            return
        
        # 3. 等待AP信息并自动连接（如果启用）
        console.print("[blue]等待设备响应...[/blue]")
        await asyncio.sleep(10)
        
        # 4. 显示连接信息
        info = controller.get_connection_info()
        console.print("\n[green]连接信息:[/green]")
        console.print(json.dumps(info, indent=2, ensure_ascii=False))
        
        # 5. 保持连接
        console.print("\n[blue]系统运行中，按Ctrl+C退出...[/blue]")
        while True:
            await asyncio.sleep(1)
    
    except KeyboardInterrupt:
        console.print("\n[yellow]收到中断信号[/yellow]")
    finally:
        await controller.cleanup()

async def _interactive_mode():
    """交互模式实现"""
    controller = ComputerController()
    
    try:
        console.print("[bold blue]蓝牙AP控制系统 - 交互模式[/bold blue]")
        
        while True:
            console.print("\n[bold]可用操作:[/bold]")
            console.print("1. 扫描并连接设备")
            console.print("2. 启动设备AP")
            console.print("3. 停止设备AP")
            console.print("4. 连接到设备AP")
            console.print("5. 断开WiFi连接")
            console.print("6. 获取设备状态")
            console.print("7. 显示连接信息")
            console.print("0. 退出")
            
            choice = Prompt.ask("请选择操作", choices=["0", "1", "2", "3", "4", "5", "6", "7"])
            
            if choice == "0":
                break
            elif choice == "1":
                await controller.scan_and_connect_device()
            elif choice == "2":
                ssid = Prompt.ask("AP名称 (可选)", default="")
                password = Prompt.ask("AP密码 (可选)", default="")
                await controller.start_device_ap(ssid or None, password or None)
            elif choice == "3":
                await controller.stop_device_ap()
            elif choice == "4":
                await controller.connect_to_device_ap()
            elif choice == "5":
                await controller.disconnect_wifi()
            elif choice == "6":
                await controller.get_device_status()
            elif choice == "7":
                info = controller.get_connection_info()
                console.print(json.dumps(info, indent=2, ensure_ascii=False))
    
    except KeyboardInterrupt:
        console.print("\n[yellow]收到中断信号[/yellow]")
    finally:
        await controller.cleanup()

async def _scan_only():
    """仅扫描设备"""
    controller = ComputerController()
    
    console.print("[blue]正在扫描蓝牙设备...[/blue]")
    devices = await controller.ble_client.scan_devices(10)
    
    if devices:
        table = Table(title="发现的设备")
        table.add_column("设备名称", style="green")
        table.add_column("地址", style="yellow")
        
        for device in devices:
            table.add_row(device.name or "未知", device.address)
        
        console.print(table)
    else:
        console.print("[red]✗[/red] 未发现目标设备")

if __name__ == "__main__":
    cli()
