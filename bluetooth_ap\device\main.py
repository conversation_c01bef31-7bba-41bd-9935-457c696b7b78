"""
设备端主程序
整合BLE服务器和WiFi AP管理器，提供完整的设备控制功能
适用于RK3588 aarch64 Ubuntu20.04 无屏幕设备
"""

import asyncio
import logging
import signal
import sys
import os
from typing import Optional
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from ble_server import BLEServer
from wifi_manager import WiFiAPManager
from common.protocol import CommandType, StatusType, APInfo

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/bluetooth_ap_device.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class DeviceController:
    """设备控制器"""
    
    def __init__(self, device_name: str = "RK3588-AP-Device", wifi_interface: str = "wlan0"):
        self.device_name = device_name
        self.wifi_interface = wifi_interface
        
        # 组件
        self.ble_server = BLEServer(device_name)
        self.wifi_manager = WiFiAPManager(wifi_interface)
        
        # 状态
        self.is_running = False
        self.default_ap_config = {
            "ssid": f"{device_name}-AP",
            "password": "12345678",
            "ip": "***********"
        }
        
        # 设置回调
        self._setup_callbacks()
    
    def _setup_callbacks(self):
        """设置回调函数"""
        self.ble_server.set_command_callback(self._handle_ble_command)
        self.ble_server.set_connect_callback(self._handle_client_connected)
        self.ble_server.set_disconnect_callback(self._handle_client_disconnected)
    
    async def _handle_ble_command(self, cmd_type: CommandType, params: dict):
        """处理BLE命令"""
        logger.info(f"收到BLE命令: {cmd_type.value}, 参数: {params}")
        
        try:
            if cmd_type == CommandType.START_AP:
                await self._handle_start_ap(params)
            elif cmd_type == CommandType.STOP_AP:
                await self._handle_stop_ap()
            elif cmd_type == CommandType.GET_STATUS:
                await self._handle_get_status()
            elif cmd_type == CommandType.GET_AP_INFO:
                await self._handle_get_ap_info()
            else:
                logger.warning(f"未知命令类型: {cmd_type.value}")
                await self.ble_server.send_status(StatusType.ERROR, f"未知命令: {cmd_type.value}")
                
        except Exception as e:
            logger.error(f"处理命令失败: {e}")
            await self.ble_server.send_status(StatusType.ERROR, str(e))
    
    async def _handle_start_ap(self, params: dict):
        """处理启动AP命令"""
        # 获取参数
        ssid = params.get('ssid', self.default_ap_config['ssid'])
        password = params.get('password', self.default_ap_config['password'])
        ip = params.get('ip', self.default_ap_config['ip'])
        
        logger.info(f"启动AP: SSID={ssid}, IP={ip}")
        
        # 如果AP已在运行，先停止
        if self.wifi_manager.is_running:
            logger.info("AP已在运行，先停止...")
            await self.wifi_manager.stop_ap()
            await asyncio.sleep(2)
        
        # 启动AP
        success = await self.wifi_manager.start_ap(ssid, password, ip)
        
        if success:
            logger.info("AP启动成功")
            await self.ble_server.send_status(StatusType.AP_RUNNING, "AP已启动")
            
            # 发送AP信息
            ap_info = APInfo(ssid, password, ip)
            await self.ble_server.send_ap_info(ap_info)
        else:
            logger.error("AP启动失败")
            await self.ble_server.send_status(StatusType.ERROR, "AP启动失败")
    
    async def _handle_stop_ap(self):
        """处理停止AP命令"""
        logger.info("停止AP")
        
        success = await self.wifi_manager.stop_ap()
        
        if success:
            logger.info("AP停止成功")
            await self.ble_server.send_status(StatusType.AP_STOPPED, "AP已停止")
        else:
            logger.error("AP停止失败")
            await self.ble_server.send_status(StatusType.ERROR, "AP停止失败")
    
    async def _handle_get_status(self):
        """处理获取状态命令"""
        status = self.wifi_manager.get_status()
        logger.info(f"当前状态: {status}")
        
        if status['running']:
            await self.ble_server.send_status(StatusType.AP_RUNNING, "AP正在运行")
        else:
            await self.ble_server.send_status(StatusType.AP_STOPPED, "AP已停止")
    
    async def _handle_get_ap_info(self):
        """处理获取AP信息命令"""
        if self.wifi_manager.current_ap_info:
            await self.ble_server.send_ap_info(self.wifi_manager.current_ap_info)
            logger.info("已发送AP信息")
        else:
            await self.ble_server.send_status(StatusType.ERROR, "AP未运行")
    
    async def _handle_client_connected(self, client):
        """处理客户端连接"""
        logger.info(f"BLE客户端已连接: {client}")
        
        # 发送当前状态
        await self._handle_get_status()
        
        # 如果AP在运行，发送AP信息
        if self.wifi_manager.is_running and self.wifi_manager.current_ap_info:
            await self.ble_server.send_ap_info(self.wifi_manager.current_ap_info)
    
    async def _handle_client_disconnected(self, client):
        """处理客户端断开"""
        logger.info(f"BLE客户端已断开: {client}")
    
    async def start(self):
        """启动设备控制器"""
        if self.is_running:
            logger.warning("设备控制器已在运行")
            return
        
        try:
            logger.info("启动设备控制器...")
            
            # 启动BLE服务器
            await self.ble_server.start()
            logger.info("BLE服务器启动成功")
            
            self.is_running = True
            logger.info(f"设备控制器启动完成: {self.device_name}")
            
            # 可选：自动启动默认AP
            auto_start_ap = os.getenv('AUTO_START_AP', 'false').lower() == 'true'
            if auto_start_ap:
                logger.info("自动启动默认AP...")
                await self._handle_start_ap(self.default_ap_config)
            
        except Exception as e:
            logger.error(f"启动设备控制器失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止设备控制器"""
        if not self.is_running:
            return
        
        logger.info("停止设备控制器...")
        
        try:
            # 停止WiFi AP
            if self.wifi_manager.is_running:
                await self.wifi_manager.stop_ap()
            
            # 停止BLE服务器
            await self.ble_server.stop()
            
            self.is_running = False
            logger.info("设备控制器已停止")
            
        except Exception as e:
            logger.error(f"停止设备控制器失败: {e}")
    
    def get_status(self) -> dict:
        """获取设备状态"""
        return {
            "device_name": self.device_name,
            "running": self.is_running,
            "ble_server": {
                "running": self.ble_server.is_running,
                "status": self.ble_server.current_status.value if self.ble_server.current_status else None
            },
            "wifi_ap": self.wifi_manager.get_status(),
            "timestamp": datetime.now().isoformat()
        }

# 全局控制器实例
controller: Optional[DeviceController] = None

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，正在停止...")
    if controller:
        asyncio.create_task(controller.stop())

async def main():
    """主函数"""
    global controller
    
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 读取配置
    device_name = os.getenv('DEVICE_NAME', 'RK3588-AP-Device')
    wifi_interface = os.getenv('WIFI_INTERFACE', 'wlan0')
    
    logger.info(f"启动设备: {device_name}, WiFi接口: {wifi_interface}")
    
    # 创建控制器
    controller = DeviceController(device_name, wifi_interface)
    
    try:
        # 启动控制器
        await controller.start()
        
        logger.info("设备运行中，按Ctrl+C停止...")
        
        # 保持运行
        while controller.is_running:
            await asyncio.sleep(1)
            
            # 定期输出状态（可选）
            if int(datetime.now().timestamp()) % 300 == 0:  # 每5分钟
                status = controller.get_status()
                logger.info(f"设备状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
    
    except KeyboardInterrupt:
        logger.info("收到中断信号")
    except Exception as e:
        logger.error(f"运行异常: {e}")
    finally:
        if controller:
            await controller.stop()
        logger.info("程序退出")

if __name__ == "__main__":
    # 检查运行权限
    if os.geteuid() != 0:
        logger.warning("建议以root权限运行以确保WiFi AP功能正常")
    
    # 运行主程序
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        sys.exit(1)
