# 🔗 蓝牙AP控制系统

通过蓝牙BLE协议控制RK3588设备创建WiFi热点，并实现电脑端自动连接的完整解决方案。

## 📋 系统概述

本系统实现了电脑通过蓝牙扫描并连接到RK3588设备，发送控制命令让设备创建WiFi AP热点，然后电脑自动连接到该热点的完整流程。

### 🎯 核心功能

- **🔍 设备发现**: 自动扫描并识别目标RK3588设备
- **📡 蓝牙通信**: 基于BLE协议的可靠命令传输
- **📶 AP管理**: 动态创建和管理WiFi热点
- **🔄 自动连接**: 智能WiFi连接和状态监控
- **🌐 Web界面**: 直观的图形化控制界面
- **💻 命令行**: 灵活的CLI控制工具

## 🏗️ 系统架构

```
┌─────────────────┐    蓝牙BLE     ┌─────────────────┐
│   电脑端        │ ◄──────────► │   RK3588设备    │
│                 │              │                 │
│ • BLE客户端     │   控制命令    │ • BLE服务器     │
│ • WiFi管理器    │   AP信息      │ • WiFi AP管理   │
│ • Web界面       │              │ • 系统服务      │
│ • CLI工具       │              │                 │
└─────────────────┘              └─────────────────┘
         │                                │
         │          WiFi连接              │
         └────────────────────────────────┘
```

### 设备端 (RK3588 Ubuntu 20.04)
- **BLE服务器**: 接收和处理控制命令
- **WiFi AP管理**: 使用hostapd和dnsmasq创建热点
- **系统集成**: systemd服务和自动启动
- **日志记录**: 完整的操作日志和错误处理

### 电脑端 (Windows/Linux)
- **BLE客户端**: 设备扫描和连接管理
- **WiFi管理**: 跨平台WiFi连接支持
- **Web界面**: 基于Flask的现代化控制面板
- **CLI工具**: 命令行自动化工具

## 🚀 快速开始

### 1. 环境要求

**设备端 (RK3588)**:
- Ubuntu 20.04 LTS
- Python 3.7+
- 蓝牙和WiFi硬件支持
- root权限

**电脑端**:
- Windows 10+ 或 Linux
- Python 3.7+
- 蓝牙适配器
- WiFi适配器

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd bluetooth_ap

# 安装Python依赖
pip install -r requirements.txt
```

### 3. 设备端部署

在RK3588设备上执行：

```bash
# 安装系统依赖和配置服务
sudo ./start_device.sh --install

# 启动服务
sudo ./start_device.sh --start

# 查看服务状态
sudo ./start_device.sh --status
```

### 4. 电脑端使用

#### Web界面模式 (推荐)
```bash
python start_computer.py web
```
然后在浏览器中访问 `http://localhost:5000`

#### 命令行模式
```bash
python start_computer.py cli
```

#### 自动模式
```bash
python start_computer.py auto --ssid "MyAP" --password "12345678"
```

## 📁 项目结构

```
bluetooth_ap/
├── device/                 # 设备端代码
│   ├── main.py            # 设备端主程序
│   ├── ble_server.py      # BLE服务器实现
│   └── wifi_manager.py    # WiFi AP管理器
├── computer/              # 电脑端代码
│   ├── main.py            # 电脑端CLI工具
│   ├── ble_client.py      # BLE客户端实现
│   └── wifi_manager.py    # WiFi连接管理器
├── common/                # 共享代码
│   └── protocol.py        # 通信协议定义
├── templates/             # Web界面模板
│   └── index.html         # 主界面
├── web_app.py             # Web应用服务器
├── start_device.sh        # 设备端启动脚本
├── start_computer.py      # 电脑端启动脚本
├── requirements.txt       # Python依赖
└── README.md              # 项目文档
```

## 🔧 详细配置

### 设备端配置

#### 环境变量
```bash
export DEVICE_NAME="RK3588-AP-Device"    # 设备名称
export WIFI_INTERFACE="wlan0"            # WiFi接口
export AUTO_START_AP="false"             # 自动启动AP
```

#### 系统服务
```bash
# 启动服务
sudo systemctl start bluetooth-ap-device

# 停止服务
sudo systemctl stop bluetooth-ap-device

# 查看日志
sudo journalctl -u bluetooth-ap-device -f
```

#### 手动运行
```bash
# 前台运行（调试模式）
sudo ./start_device.sh --foreground

# 或直接运行Python脚本
sudo python3 device/main.py
```

### 电脑端配置

#### Web界面选项
```bash
# 自定义地址和端口
python start_computer.py web --host 0.0.0.0 --port 8080

# 不自动打开浏览器
python start_computer.py web --no-browser
```

#### CLI工具选项
```bash
# 交互模式
python start_computer.py cli

# 或直接使用
python computer/main.py interactive
```

#### 自动模式选项
```bash
# 完全自动化
python start_computer.py auto

# 指定AP参数
python start_computer.py auto --ssid "CustomAP" --password "mypassword"

# 不自动连接WiFi
python start_computer.py auto --no-wifi
```

## 🔌 通信协议

### BLE服务定义
- **服务UUID**: `12345678-1234-1234-1234-123456789abc`
- **命令特征**: `*************-4321-4321-cba987654321` (写入)
- **状态特征**: `11111111-**************-************` (读取/通知)
- **AP信息特征**: `aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee` (读取/通知)

### 命令格式
```json
{
  "type": "start_ap|stop_ap|get_status|get_ap_info",
  "params": {
    "ssid": "AP名称",
    "password": "AP密码",
    "ip": "***********"
  }
}
```

### 状态响应
```json
{
  "status": "ap_running|ap_stopped|error|connected|disconnected",
  "message": "状态描述"
}
```

### AP信息
```json
{
  "ssid": "AP名称",
  "password": "AP密码",
  "ip": "***********"
}
```

## 🛠️ 故障排除

### 常见问题

#### 1. 设备端蓝牙服务启动失败
```bash
# 检查蓝牙服务状态
sudo systemctl status bluetooth

# 重启蓝牙服务
sudo systemctl restart bluetooth

# 解除蓝牙阻止
sudo rfkill unblock bluetooth
```

#### 2. WiFi AP创建失败
```bash
# 检查网络接口
ip link show

# 检查hostapd和dnsmasq
sudo systemctl status hostapd
sudo systemctl status dnsmasq

# 查看详细错误
sudo journalctl -u bluetooth-ap-device -n 50
```

#### 3. 电脑端无法发现设备
```bash
# 检查蓝牙适配器
python -c "import bleak; print('BLE支持正常')"

# 手动扫描测试
python -c "
import asyncio
from bleak import BleakScanner
async def scan():
    devices = await BleakScanner.discover()
    for d in devices:
        print(f'{d.name}: {d.address}')
asyncio.run(scan())
"
```

#### 4. WiFi连接失败
- **Windows**: 确保WiFi适配器驱动正常
- **Linux**: 检查NetworkManager或wpa_supplicant状态
- 确认AP密码正确
- 检查信号强度

### 日志分析

#### 设备端日志
```bash
# 系统日志
sudo journalctl -u bluetooth-ap-device -f

# 应用日志
sudo tail -f /var/log/bluetooth_ap_device.log
```

#### 电脑端日志
- Web模式：浏览器控制台和终端输出
- CLI模式：终端输出
- 自动模式：详细的操作日志

## 🔒 安全考虑

### 网络安全
- AP密码应使用强密码（8位以上）
- 考虑使用WPA3加密（如果硬件支持）
- 限制AP连接设备数量
- 定期更换AP密码

### 蓝牙安全
- BLE连接使用配对验证
- 命令传输加密
- 限制连接设备数量
- 定期检查连接状态

### 系统安全
- 设备端以最小权限运行
- 定期更新系统和依赖
- 监控异常连接和操作
- 备份重要配置

## 🚀 性能优化

### 设备端优化
- 使用systemd服务管理
- 优化蓝牙扫描间隔
- 缓存AP配置信息
- 异步处理命令

### 电脑端优化
- 连接池管理
- 智能重连机制
- 缓存设备信息
- 并发操作支持

## 🔄 开发和扩展

### 添加新功能
1. 在`common/protocol.py`中定义新的命令类型
2. 在设备端`ble_server.py`中添加命令处理
3. 在电脑端`ble_client.py`中添加命令发送
4. 更新Web界面和CLI工具

### 自定义配置
- 修改UUID以避免冲突
- 调整超时和重试参数
- 自定义AP配置模板
- 扩展日志格式

### 测试
```bash
# 运行设备端测试
python device/ble_server.py

# 运行电脑端测试
python computer/ble_client.py

# 测试WiFi管理
python computer/wifi_manager.py
```

## 📞 技术支持

### 问题反馈
- 提供详细的错误日志
- 说明操作系统和硬件信息
- 描述复现步骤
- 附上配置文件

### 贡献代码
1. Fork项目
2. 创建功能分支
3. 提交代码
4. 发起Pull Request

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🙏 致谢

感谢以下开源项目的支持：
- [Bleak](https://github.com/hbldh/bleak) - Python BLE库
- [Flask](https://flask.palletsprojects.com/) - Web框架
- [Click](https://click.palletsprojects.com/) - CLI工具
- [Rich](https://github.com/Textualize/rich) - 终端美化