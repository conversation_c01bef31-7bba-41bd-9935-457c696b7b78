from flask import Flask, render_template, jsonify, request
from flask_socketio import <PERSON><PERSON><PERSON>, emit
import asyncio
import threading
import json
import logging
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

from computer.ble_client import BLE<PERSON>lient
from computer.wifi_manager import WiFiManager
from common.protocol import APInfo, StatusType

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'
socketio = SocketIO(app, cors_allowed_origins="*")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局控制器
ble_client = BLEClient()
wifi_manager = WiFiManager()

# 设置BLE客户端回调
async def on_status_changed(status, message):
    """处理设备状态变化"""
    global device_status

    if status == StatusType.AP_RUNNING:
        device_status['ap_running'] = True
        device_status['connected'] = True
    elif status == StatusType.AP_STOPPED:
        device_status['ap_running'] = False
    elif status == StatusType.ERROR:
        pass  # 错误状态不改变连接状态

    device_status['last_update'] = datetime.now().isoformat()

    socketio.emit('status_update', {
        'device': device_status,
        'bluetooth': bluetooth_status
    })

    socketio.emit('log', {
        'message': f'设备状态: {status.value} - {message}',
        'type': 'info' if status != StatusType.ERROR else 'error'
    })

async def on_ap_info_received(ap_info):
    """处理AP信息"""
    global device_status

    device_status['ap_info'] = {
        'ssid': ap_info.ssid,
        'password': ap_info.password,
        'ip': ap_info.ip
    }
    device_status['last_update'] = datetime.now().isoformat()

    socketio.emit('status_update', {
        'device': device_status,
        'bluetooth': bluetooth_status
    })

    socketio.emit('log', {
        'message': f'收到AP信息: SSID={ap_info.ssid}, IP={ap_info.ip}'
    })

    # 可选：自动连接WiFi
    auto_connect = True  # 可以通过配置控制
    if auto_connect:
        socketio.emit('log', {'message': '正在自动连接到设备AP...'})
        threading.Thread(target=connect_wifi_async, args=(ap_info,)).start()

async def on_ble_disconnected():
    """处理BLE断开连接"""
    global bluetooth_status, device_status

    bluetooth_status['connected'] = False
    bluetooth_status['device_name'] = None
    bluetooth_status['device_address'] = None

    device_status['connected'] = False
    device_status['ap_running'] = False
    device_status['ap_info'] = None
    device_status['last_update'] = datetime.now().isoformat()

    socketio.emit('bluetooth_status', bluetooth_status)
    socketio.emit('status_update', {
        'device': device_status,
        'bluetooth': bluetooth_status
    })
    socketio.emit('log', {'message': 'BLE连接已断开', 'type': 'warning'})

def connect_wifi_async(ap_info):
    """异步连接WiFi"""
    async def _connect():
        try:
            success = await wifi_manager.connect_to_ap(ap_info)

            if success:
                socketio.emit('log', {'message': f'成功连接到设备AP: {ap_info.ssid}'})
                socketio.emit('log', {'message': f'可通过 {ap_info.ip} 访问设备'})
            else:
                socketio.emit('log', {'message': f'连接设备AP失败: {ap_info.ssid}', 'type': 'error'})

        except Exception as e:
            logger.error(f"连接WiFi失败: {e}")
            socketio.emit('log', {'message': f'连接WiFi失败: {str(e)}', 'type': 'error'})

    # 在新的事件循环中运行
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(_connect())
    finally:
        loop.close()

# 设置回调
ble_client.set_status_callback(on_status_changed)
ble_client.set_ap_info_callback(on_ap_info_received)
ble_client.set_disconnect_callback(on_ble_disconnected)

# 全局状态
device_status = {
    'connected': False,
    'ap_running': False,
    'ap_info': None,
    'last_update': None
}

# 蓝牙连接状态
bluetooth_status = {
    'connected': False,
    'device_name': None,
    'device_address': None
}

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """获取系统状态"""
    return jsonify({
        'device': device_status,
        'bluetooth': bluetooth_status,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/connect', methods=['POST'])
def connect_device():
    """连接设备"""
    data = request.get_json()
    device_address = data.get('address')
    
    if not device_address:
        return jsonify({'error': '设备地址不能为空'}), 400
    
    # 启动蓝牙连接线程
    threading.Thread(target=connect_bluetooth_device, args=(device_address,)).start()
    
    return jsonify({'message': '正在连接设备...'})

@app.route('/api/control', methods=['POST'])
def control_ap():
    """控制AP"""
    data = request.get_json()
    action = data.get('action')
    ssid = data.get('ssid', '')
    password = data.get('password', '')

    if not bluetooth_status['connected']:
        return jsonify({'error': '设备未连接'}), 400

    if action not in ['start', 'stop']:
        return jsonify({'error': '无效的操作'}), 400

    # 启动后台任务
    threading.Thread(target=control_ap_async, args=(action, ssid, password)).start()

    return jsonify({'message': f'正在{action} AP...'})

def control_ap_async(action, ssid, password):
    """异步控制AP"""
    async def _control():
        try:
            if action == 'start':
                socketio.emit('log', {'message': f'正在启动AP: {ssid or "默认名称"}'})
                success = await ble_client.start_ap(ssid or None, password or None)

                if success:
                    socketio.emit('log', {'message': 'AP启动命令已发送'})
                else:
                    socketio.emit('log', {'message': 'AP启动命令发送失败', 'type': 'error'})

            elif action == 'stop':
                socketio.emit('log', {'message': '正在停止AP'})
                success = await ble_client.stop_ap()

                if success:
                    socketio.emit('log', {'message': 'AP停止命令已发送'})
                else:
                    socketio.emit('log', {'message': 'AP停止命令发送失败', 'type': 'error'})

        except Exception as e:
            logger.error(f"控制AP失败: {e}")
            socketio.emit('log', {'message': f'控制AP失败: {str(e)}', 'type': 'error'})

    # 在新的事件循环中运行
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(_control())
    finally:
        loop.close()

def connect_bluetooth_device(address):
    """连接蓝牙设备"""
    async def _connect():
        try:
            # 扫描设备
            socketio.emit('log', {'message': '正在扫描蓝牙设备...'})
            devices = await ble_client.scan_devices(10)

            # 查找目标设备
            target_device = None
            for device in devices:
                if device.address.lower() == address.lower():
                    target_device = device
                    break

            if not target_device:
                socketio.emit('log', {'message': f'未找到设备: {address}', 'type': 'error'})
                return

            # 连接设备
            socketio.emit('log', {'message': f'正在连接设备: {target_device.name}'})
            success = await ble_client.connect(target_device)

            if success:
                bluetooth_status['connected'] = True
                bluetooth_status['device_address'] = address
                bluetooth_status['device_name'] = target_device.name or f"Device-{address[-6:]}"

                socketio.emit('bluetooth_status', bluetooth_status)
                socketio.emit('log', {'message': f'成功连接到设备: {target_device.name}'})

                # 获取设备状态
                await ble_client.get_status()
            else:
                socketio.emit('log', {'message': f'连接设备失败: {target_device.name}', 'type': 'error'})

        except Exception as e:
            logger.error(f"连接蓝牙设备失败: {e}")
            socketio.emit('log', {'message': f'连接失败: {str(e)}', 'type': 'error'})

    # 在新的事件循环中运行
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(_connect())
    finally:
        loop.close()

@socketio.on('connect')
def handle_connect():
    """客户端连接"""
    emit('status_update', {
        'device': device_status,
        'bluetooth': bluetooth_status
    })

@socketio.on('device_status')
def handle_device_status(data):
    """处理设备状态更新"""
    global device_status
    device_status.update(data)
    device_status['last_update'] = datetime.now().isoformat()
    
    emit('status_update', {
        'device': device_status,
        'bluetooth': bluetooth_status
    }, broadcast=True)

@socketio.on('log')
def handle_log(data):
    """处理日志"""
    emit('log', data, broadcast=True)

if __name__ == '__main__':
    socketio.run(app, debug=True, host='0.0.0.0', port=5000) 