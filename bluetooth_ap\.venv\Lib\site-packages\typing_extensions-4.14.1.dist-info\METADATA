Metadata-Version: 2.4
Name: typing_extensions
Version: 4.14.1
Summary: Backported and Experimental Type Hints for Python 3.9+
Keywords: annotations,backport,checker,checking,function,hinting,hints,type,typechecking,typehinting,typehints,typing
Author-email: "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>" <lev<PERSON><PERSON><PERSON>@gmail.com>
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-Expression: PSF-2.0
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3.14
Classifier: Topic :: Software Development
License-File: LICENSE
Project-URL: Bug Tracker, https://github.com/python/typing_extensions/issues
Project-URL: Changes, https://github.com/python/typing_extensions/blob/main/CHANGELOG.md
Project-URL: Documentation, https://typing-extensions.readthedocs.io/
Project-URL: Home, https://github.com/python/typing_extensions
Project-URL: Q & A, https://github.com/python/typing/discussions
Project-URL: Repository, https://github.com/python/typing_extensions

# Typing Extensions

[![Chat at https://gitter.im/python/typing](https://badges.gitter.im/python/typing.svg)](https://gitter.im/python/typing)

[Documentation](https://typing-extensions.readthedocs.io/en/latest/#) –
[PyPI](https://pypi.org/project/typing-extensions/)

## Overview

The `typing_extensions` module serves two related purposes:

- Enable use of new type system features on older Python versions. For example,
  `typing.TypeGuard` is new in Python 3.10, but `typing_extensions` allows
  users on previous Python versions to use it too.
- Enable experimentation with new type system PEPs before they are accepted and
  added to the `typing` module.

`typing_extensions` is treated specially by static type checkers such as
mypy and pyright. Objects defined in `typing_extensions` are treated the same
way as equivalent forms in `typing`.

`typing_extensions` uses
[Semantic Versioning](https://semver.org/). The
major version will be incremented only for backwards-incompatible changes.
Therefore, it's safe to depend
on `typing_extensions` like this: `typing_extensions >=x.y, <(x+1)`,
where `x.y` is the first version that includes all features you need.

## Included items

See [the documentation](https://typing-extensions.readthedocs.io/en/latest/#) for a
complete listing of module contents.

## Contributing

See [CONTRIBUTING.md](https://github.com/python/typing_extensions/blob/main/CONTRIBUTING.md)
for how to contribute to `typing_extensions`.

