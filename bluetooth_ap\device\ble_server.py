"""
设备端蓝牙BLE服务器
用于接收电脑端的控制命令并返回状态信息
"""

import asyncio
import logging
from typing import Optional, Callable
import json
from bleak import BleakServer, BleakGATTCharacteristic, BleakGATTService
from bleak.backends.characteristic import BleakGATTCharacteristicProperties
import sys
import os

# 添加common目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from common.protocol import (
    AP_CONTROL_SERVICE_UUID,
    COMMAND_CHARACTERISTIC_UUID,
    STATUS_CHARACTERISTIC_UUID,
    AP_INFO_CHARACTERISTIC_UUID,
    Protocol,
    CommandType,
    StatusType,
    APInfo
)

logger = logging.getLogger(__name__)

class BLEServer:
    """BLE服务器类"""
    
    def __init__(self, device_name: str = "RK3588-AP-Device"):
        self.device_name = device_name
        self.server: Optional[BleakServer] = None
        self.is_running = False
        
        # 回调函数
        self.on_command_received: Optional[Callable] = None
        self.on_client_connected: Optional[Callable] = None
        self.on_client_disconnected: Optional[Callable] = None
        
        # 特征值引用
        self.command_char: Optional[BleakGATTCharacteristic] = None
        self.status_char: Optional[BleakGATTCharacteristic] = None
        self.ap_info_char: Optional[BleakGATTCharacteristic] = None
        
        # 当前状态
        self.current_status = StatusType.DISCONNECTED
        self.current_ap_info: Optional[APInfo] = None
        
    async def setup_service(self) -> BleakGATTService:
        """设置GATT服务"""
        service = BleakGATTService(AP_CONTROL_SERVICE_UUID, True)
        
        # 命令特征值 (可写)
        self.command_char = BleakGATTCharacteristic(
            COMMAND_CHARACTERISTIC_UUID,
            BleakGATTCharacteristicProperties.write,
            None,
            [self._on_command_write]
        )
        service.add_characteristic(self.command_char)
        
        # 状态特征值 (可读、可通知)
        self.status_char = BleakGATTCharacteristic(
            STATUS_CHARACTERISTIC_UUID,
            BleakGATTCharacteristicProperties.read | BleakGATTCharacteristicProperties.notify,
            None,
            [self._on_status_read]
        )
        service.add_characteristic(self.status_char)
        
        # AP信息特征值 (可读、可通知)
        self.ap_info_char = BleakGATTCharacteristic(
            AP_INFO_CHARACTERISTIC_UUID,
            BleakGATTCharacteristicProperties.read | BleakGATTCharacteristicProperties.notify,
            None,
            [self._on_ap_info_read]
        )
        service.add_characteristic(self.ap_info_char)
        
        return service
        
    async def _on_command_write(self, characteristic: BleakGATTCharacteristic, data: bytes):
        """处理命令写入"""
        try:
            cmd_type, params = Protocol.decode_command(data)
            logger.info(f"收到命令: {cmd_type.value}, 参数: {params}")
            
            if self.on_command_received:
                await self.on_command_received(cmd_type, params)
                
        except Exception as e:
            logger.error(f"处理命令时出错: {e}")
            await self.send_status(StatusType.ERROR, str(e))
    
    async def _on_status_read(self, characteristic: BleakGATTCharacteristic) -> bytes:
        """处理状态读取"""
        return Protocol.encode_status(self.current_status, "")
    
    async def _on_ap_info_read(self, characteristic: BleakGATTCharacteristic) -> bytes:
        """处理AP信息读取"""
        if self.current_ap_info:
            return Protocol.encode_ap_info(self.current_ap_info)
        else:
            return b""
    
    async def send_status(self, status: StatusType, message: str = ""):
        """发送状态更新"""
        self.current_status = status
        if self.server and self.status_char:
            data = Protocol.encode_status(status, message)
            await self.server.notify(self.status_char, data)
            logger.info(f"发送状态: {status.value}, 消息: {message}")
    
    async def send_ap_info(self, ap_info: APInfo):
        """发送AP信息"""
        self.current_ap_info = ap_info
        if self.server and self.ap_info_char:
            data = Protocol.encode_ap_info(ap_info)
            await self.server.notify(self.ap_info_char, data)
            logger.info(f"发送AP信息: SSID={ap_info.ssid}, IP={ap_info.ip}")
    
    async def start(self):
        """启动BLE服务器"""
        if self.is_running:
            logger.warning("BLE服务器已在运行")
            return
            
        try:
            # 创建服务
            service = await self.setup_service()
            
            # 创建服务器
            self.server = BleakServer()
            self.server.add_service(service)
            
            # 设置连接回调
            self.server.set_connect_callback(self._on_connect)
            self.server.set_disconnect_callback(self._on_disconnect)
            
            # 启动服务器
            await self.server.start()
            self.is_running = True
            
            logger.info(f"BLE服务器已启动，设备名: {self.device_name}")
            await self.send_status(StatusType.CONNECTED, "BLE服务器已启动")
            
        except Exception as e:
            logger.error(f"启动BLE服务器失败: {e}")
            raise
    
    async def stop(self):
        """停止BLE服务器"""
        if not self.is_running:
            return
            
        try:
            if self.server:
                await self.server.stop()
                self.server = None
            
            self.is_running = False
            self.current_status = StatusType.DISCONNECTED
            
            logger.info("BLE服务器已停止")
            
        except Exception as e:
            logger.error(f"停止BLE服务器失败: {e}")
    
    async def _on_connect(self, client):
        """客户端连接回调"""
        logger.info(f"客户端已连接: {client}")
        await self.send_status(StatusType.CONNECTED, "客户端已连接")
        
        if self.on_client_connected:
            await self.on_client_connected(client)
    
    async def _on_disconnect(self, client):
        """客户端断开回调"""
        logger.info(f"客户端已断开: {client}")
        await self.send_status(StatusType.DISCONNECTED, "客户端已断开")
        
        if self.on_client_disconnected:
            await self.on_client_disconnected(client)
    
    def set_command_callback(self, callback: Callable):
        """设置命令接收回调"""
        self.on_command_received = callback
    
    def set_connect_callback(self, callback: Callable):
        """设置连接回调"""
        self.on_client_connected = callback
    
    def set_disconnect_callback(self, callback: Callable):
        """设置断开回调"""
        self.on_client_disconnected = callback

# 测试代码
async def test_ble_server():
    """测试BLE服务器"""
    logging.basicConfig(level=logging.INFO)
    
    server = BLEServer()
    
    async def handle_command(cmd_type: CommandType, params: dict):
        print(f"处理命令: {cmd_type.value}, 参数: {params}")
        
        if cmd_type == CommandType.START_AP:
            await server.send_status(StatusType.AP_RUNNING, "AP已启动")
            # 模拟AP信息
            ap_info = APInfo("TestAP", "12345678", "192.168.4.1")
            await server.send_ap_info(ap_info)
        elif cmd_type == CommandType.STOP_AP:
            await server.send_status(StatusType.AP_STOPPED, "AP已停止")
    
    server.set_command_callback(handle_command)
    
    try:
        await server.start()
        print("BLE服务器运行中，按Ctrl+C停止...")
        
        # 保持运行
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("正在停止服务器...")
    finally:
        await server.stop()

if __name__ == "__main__":
    asyncio.run(test_ble_server())
