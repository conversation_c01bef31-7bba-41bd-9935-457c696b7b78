"""
电脑端WiFi连接管理器
用于自动连接到设备创建的AP热点
"""

import asyncio
import logging
import platform
import subprocess
import time
from typing import Optional, List, Dict, Any, Callable
import sys
import os

# 根据操作系统选择WiFi库
if platform.system() == "Windows":
    try:
        import pywifi
        from pywifi import const
        WIFI_LIB = "pywifi"
    except ImportError:
        WIFI_LIB = "netsh"
elif platform.system() == "Linux":
    WIFI_LIB = "nmcli"
else:
    WIFI_LIB = "unsupported"

# 添加common目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from common.protocol import APInfo

logger = logging.getLogger(__name__)

class WiFiManager:
    """WiFi连接管理器"""
    
    def __init__(self):
        self.current_connection: Optional[Dict[str, Any]] = None
        self.is_connected = False
        self.wifi_interface = None
        
        # 回调函数
        self.on_connected: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None
        self.on_connection_failed: Optional[Callable] = None
        
        # 初始化WiFi接口
        self._init_wifi_interface()
    
    def _init_wifi_interface(self):
        """初始化WiFi接口"""
        if WIFI_LIB == "pywifi":
            try:
                wifi = pywifi.PyWiFi()
                self.wifi_interface = wifi.interfaces()[0]  # 使用第一个WiFi接口
                logger.info(f"使用WiFi接口: {self.wifi_interface.name()}")
            except Exception as e:
                logger.error(f"初始化pywifi失败: {e}")
                self.wifi_interface = None
        else:
            logger.info(f"使用WiFi库: {WIFI_LIB}")
    
    async def scan_networks(self, timeout: float = 10.0) -> List[Dict[str, Any]]:
        """扫描WiFi网络"""
        networks = []
        
        try:
            if WIFI_LIB == "pywifi":
                networks = await self._scan_with_pywifi(timeout)
            elif WIFI_LIB == "netsh":
                networks = await self._scan_with_netsh()
            elif WIFI_LIB == "nmcli":
                networks = await self._scan_with_nmcli()
            else:
                logger.error("不支持的操作系统")
                
        except Exception as e:
            logger.error(f"扫描WiFi网络失败: {e}")
        
        logger.info(f"扫描到 {len(networks)} 个WiFi网络")
        return networks
    
    async def _scan_with_pywifi(self, timeout: float) -> List[Dict[str, Any]]:
        """使用pywifi扫描网络"""
        if not self.wifi_interface:
            return []
        
        networks = []
        try:
            self.wifi_interface.scan()
            await asyncio.sleep(2)  # 等待扫描完成
            
            scan_results = self.wifi_interface.scan_results()
            
            for network in scan_results:
                networks.append({
                    "ssid": network.ssid,
                    "signal": network.signal,
                    "auth": network.auth,
                    "cipher": network.cipher
                })
                
        except Exception as e:
            logger.error(f"pywifi扫描失败: {e}")
        
        return networks
    
    async def _scan_with_netsh(self) -> List[Dict[str, Any]]:
        """使用netsh扫描网络（Windows）"""
        networks = []
        try:
            result = subprocess.run(
                ["netsh", "wlan", "show", "profiles"],
                capture_output=True,
                text=True,
                encoding='gbk'
            )
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if "所有用户配置文件" in line or "All User Profile" in line:
                        ssid = line.split(':')[-1].strip()
                        if ssid:
                            networks.append({
                                "ssid": ssid,
                                "signal": -50,  # 默认信号强度
                                "auth": "WPA2",
                                "cipher": "AES"
                            })
                            
        except Exception as e:
            logger.error(f"netsh扫描失败: {e}")
        
        return networks
    
    async def _scan_with_nmcli(self) -> List[Dict[str, Any]]:
        """使用nmcli扫描网络（Linux）"""
        networks = []
        try:
            result = subprocess.run(
                ["nmcli", "-t", "-f", "SSID,SIGNAL,SECURITY", "dev", "wifi"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line:
                        parts = line.split(':')
                        if len(parts) >= 3:
                            networks.append({
                                "ssid": parts[0],
                                "signal": int(parts[1]) if parts[1].isdigit() else -50,
                                "auth": parts[2] if parts[2] else "Open",
                                "cipher": "AES"
                            })
                            
        except Exception as e:
            logger.error(f"nmcli扫描失败: {e}")
        
        return networks
    
    async def connect_to_ap(self, ap_info: APInfo, timeout: float = 30.0) -> bool:
        """连接到指定的AP"""
        logger.info(f"正在连接到AP: {ap_info.ssid}")
        
        try:
            if WIFI_LIB == "pywifi":
                success = await self._connect_with_pywifi(ap_info, timeout)
            elif WIFI_LIB == "netsh":
                success = await self._connect_with_netsh(ap_info, timeout)
            elif WIFI_LIB == "nmcli":
                success = await self._connect_with_nmcli(ap_info, timeout)
            else:
                logger.error("不支持的操作系统")
                return False
            
            if success:
                self.is_connected = True
                self.current_connection = {
                    "ssid": ap_info.ssid,
                    "ip": ap_info.ip,
                    "connected_at": time.time()
                }
                
                logger.info(f"成功连接到AP: {ap_info.ssid}")
                
                if self.on_connected:
                    await self.on_connected(ap_info)
                
                return True
            else:
                logger.error(f"连接AP失败: {ap_info.ssid}")
                
                if self.on_connection_failed:
                    await self.on_connection_failed(ap_info)
                
                return False
                
        except Exception as e:
            logger.error(f"连接AP异常: {e}")
            return False
    
    async def _connect_with_pywifi(self, ap_info: APInfo, timeout: float) -> bool:
        """使用pywifi连接"""
        if not self.wifi_interface:
            return False
        
        try:
            # 断开当前连接
            self.wifi_interface.disconnect()
            await asyncio.sleep(1)
            
            # 创建配置文件
            profile = pywifi.Profile()
            profile.ssid = ap_info.ssid
            profile.auth = const.AUTH_ALG_OPEN
            profile.akm.append(const.AKM_TYPE_WPA2PSK)
            profile.cipher = const.CIPHER_TYPE_CCMP
            profile.key = ap_info.password
            
            # 删除已存在的配置文件
            self.wifi_interface.remove_all_network_profiles()
            
            # 添加配置文件
            tmp_profile = self.wifi_interface.add_network_profile(profile)
            
            # 连接
            self.wifi_interface.connect(tmp_profile)
            
            # 等待连接完成
            start_time = time.time()
            while time.time() - start_time < timeout:
                if self.wifi_interface.status() == const.IFACE_CONNECTED:
                    return True
                await asyncio.sleep(1)
            
            return False
            
        except Exception as e:
            logger.error(f"pywifi连接失败: {e}")
            return False
    
    async def _connect_with_netsh(self, ap_info: APInfo, timeout: float) -> bool:
        """使用netsh连接（Windows）"""
        try:
            # 创建配置文件XML
            profile_xml = f"""<?xml version="1.0"?>
<WLANProfile xmlns="http://www.microsoft.com/networking/WLAN/profile/v1">
    <name>{ap_info.ssid}</name>
    <SSIDConfig>
        <SSID>
            <name>{ap_info.ssid}</name>
        </SSID>
    </SSIDConfig>
    <connectionType>ESS</connectionType>
    <connectionMode>auto</connectionMode>
    <MSM>
        <security>
            <authEncryption>
                <authentication>WPA2PSK</authentication>
                <encryption>AES</encryption>
                <useOneX>false</useOneX>
            </authEncryption>
            <sharedKey>
                <keyType>passPhrase</keyType>
                <protected>false</protected>
                <keyMaterial>{ap_info.password}</keyMaterial>
            </sharedKey>
        </security>
    </MSM>
</WLANProfile>"""
            
            # 保存配置文件
            profile_path = f"temp_{ap_info.ssid}.xml"
            with open(profile_path, 'w', encoding='utf-8') as f:
                f.write(profile_xml)
            
            try:
                # 添加配置文件
                subprocess.run([
                    "netsh", "wlan", "add", "profile", 
                    f"filename={profile_path}"
                ], check=True)
                
                # 连接
                subprocess.run([
                    "netsh", "wlan", "connect", 
                    f"name={ap_info.ssid}"
                ], check=True)
                
                # 等待连接完成
                await asyncio.sleep(5)
                
                # 检查连接状态
                result = subprocess.run([
                    "netsh", "wlan", "show", "interfaces"
                ], capture_output=True, text=True, encoding='gbk')
                
                if ap_info.ssid in result.stdout:
                    return True
                
            finally:
                # 清理临时文件
                if os.path.exists(profile_path):
                    os.remove(profile_path)
            
            return False
            
        except Exception as e:
            logger.error(f"netsh连接失败: {e}")
            return False
    
    async def _connect_with_nmcli(self, ap_info: APInfo, timeout: float) -> bool:
        """使用nmcli连接（Linux）"""
        try:
            # 连接到网络
            result = subprocess.run([
                "nmcli", "dev", "wifi", "connect", ap_info.ssid,
                "password", ap_info.password
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                # 等待连接稳定
                await asyncio.sleep(3)
                return True
            else:
                logger.error(f"nmcli连接失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"nmcli连接失败: {e}")
            return False
    
    async def disconnect(self) -> bool:
        """断开当前WiFi连接"""
        if not self.is_connected:
            return True
        
        try:
            if WIFI_LIB == "pywifi" and self.wifi_interface:
                self.wifi_interface.disconnect()
            elif WIFI_LIB == "netsh":
                subprocess.run(["netsh", "wlan", "disconnect"], check=True)
            elif WIFI_LIB == "nmcli":
                subprocess.run(["nmcli", "dev", "disconnect", "wifi"], check=True)
            
            self.is_connected = False
            old_connection = self.current_connection
            self.current_connection = None
            
            logger.info("WiFi连接已断开")
            
            if self.on_disconnected and old_connection:
                await self.on_disconnected(old_connection)
            
            return True
            
        except Exception as e:
            logger.error(f"断开WiFi连接失败: {e}")
            return False
    
    def get_connection_status(self) -> Dict[str, Any]:
        """获取连接状态"""
        return {
            "connected": self.is_connected,
            "current_connection": self.current_connection,
            "wifi_lib": WIFI_LIB
        }
    
    def set_connect_callback(self, callback: Callable):
        """设置连接成功回调"""
        self.on_connected = callback
    
    def set_disconnect_callback(self, callback: Callable):
        """设置断开连接回调"""
        self.on_disconnected = callback
    
    def set_connection_failed_callback(self, callback: Callable):
        """设置连接失败回调"""
        self.on_connection_failed = callback

# 测试代码
async def test_wifi_manager():
    """测试WiFi管理器"""
    logging.basicConfig(level=logging.INFO)
    
    manager = WiFiManager()
    
    async def on_connected(ap_info):
        print(f"WiFi连接成功: {ap_info.ssid}")
    
    async def on_disconnected(connection_info):
        print(f"WiFi连接断开: {connection_info['ssid']}")
    
    manager.set_connect_callback(on_connected)
    manager.set_disconnect_callback(on_disconnected)
    
    try:
        # 扫描网络
        print("扫描WiFi网络...")
        networks = await manager.scan_networks()
        
        for network in networks[:5]:  # 显示前5个网络
            print(f"SSID: {network['ssid']}, 信号: {network['signal']}")
        
        # 测试连接（需要提供真实的AP信息）
        # test_ap = APInfo("TestAP", "12345678", "192.168.4.1")
        # await manager.connect_to_ap(test_ap)
        
    except KeyboardInterrupt:
        print("正在断开连接...")
    finally:
        await manager.disconnect()

if __name__ == "__main__":
    asyncio.run(test_wifi_manager())
