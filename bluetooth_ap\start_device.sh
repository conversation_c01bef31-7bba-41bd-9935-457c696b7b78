#!/bin/bash

# RK3588设备端启动脚本
# 用于在Ubuntu 20.04系统上启动蓝牙AP控制服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请以root权限运行此脚本"
        log_info "使用: sudo $0"
        exit 1
    fi
}

# 检查系统依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Python版本
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    if [[ $(echo "$python_version >= 3.7" | bc -l) -eq 0 ]]; then
        log_error "需要Python 3.7或更高版本，当前版本: $python_version"
        exit 1
    fi
    
    log_success "Python版本检查通过: $python_version"
    
    # 检查必要的系统包
    packages=("hostapd" "dnsmasq" "iptables" "iw" "rfkill")
    missing_packages=()
    
    for package in "${packages[@]}"; do
        if ! dpkg -l | grep -q "^ii  $package "; then
            missing_packages+=("$package")
        fi
    done
    
    if [ ${#missing_packages[@]} -ne 0 ]; then
        log_warning "缺少以下系统包: ${missing_packages[*]}"
        log_info "正在安装缺少的包..."
        
        apt update
        apt install -y "${missing_packages[@]}"
        
        log_success "系统包安装完成"
    else
        log_success "系统包检查通过"
    fi
}

# 检查网络接口
check_network_interface() {
    local interface=${1:-wlan0}
    
    log_info "检查网络接口: $interface"
    
    if ! ip link show "$interface" &> /dev/null; then
        log_error "网络接口 $interface 不存在"
        log_info "可用的网络接口:"
        ip link show | grep -E "^[0-9]+:" | cut -d':' -f2 | sed 's/^ */  /'
        exit 1
    fi
    
    log_success "网络接口 $interface 检查通过"
}

# 安装Python依赖
install_python_deps() {
    log_info "安装Python依赖..."
    
    # 检查pip
    if ! command -v pip3 &> /dev/null; then
        log_info "安装pip3..."
        apt install -y python3-pip
    fi
    
    # 安装项目依赖
    if [ -f "requirements.txt" ]; then
        pip3 install -r requirements.txt
        log_success "Python依赖安装完成"
    else
        log_warning "requirements.txt 文件不存在，跳过Python依赖安装"
    fi
}

# 配置系统服务
setup_system_service() {
    log_info "配置系统服务..."
    
    # 创建systemd服务文件
    cat > /etc/systemd/system/bluetooth-ap-device.service << EOF
[Unit]
Description=Bluetooth AP Control Device Service
After=network.target bluetooth.service
Wants=bluetooth.service

[Service]
Type=simple
User=root
WorkingDirectory=$(pwd)
Environment=PYTHONPATH=$(pwd)
Environment=DEVICE_NAME=RK3588-AP-Device
Environment=WIFI_INTERFACE=${WIFI_INTERFACE:-wlan0}
Environment=AUTO_START_AP=false
ExecStart=/usr/bin/python3 device/main.py
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd
    systemctl daemon-reload
    
    log_success "系统服务配置完成"
}

# 启用蓝牙服务
enable_bluetooth() {
    log_info "启用蓝牙服务..."
    
    # 启动蓝牙服务
    systemctl enable bluetooth
    systemctl start bluetooth
    
    # 检查蓝牙状态
    if systemctl is-active --quiet bluetooth; then
        log_success "蓝牙服务已启动"
    else
        log_error "蓝牙服务启动失败"
        exit 1
    fi
    
    # 确保蓝牙设备未被阻止
    if command -v rfkill &> /dev/null; then
        rfkill unblock bluetooth
        log_info "蓝牙设备已解除阻止"
    fi
}

# 配置网络
configure_network() {
    log_info "配置网络设置..."
    
    # 启用IP转发
    echo 'net.ipv4.ip_forward=1' >> /etc/sysctl.conf
    sysctl -p
    
    log_success "网络配置完成"
}

# 启动服务
start_service() {
    local mode=${1:-service}
    
    if [ "$mode" = "service" ]; then
        log_info "启动系统服务..."
        systemctl enable bluetooth-ap-device
        systemctl start bluetooth-ap-device
        
        log_success "服务已启动"
        log_info "使用以下命令查看服务状态:"
        log_info "  systemctl status bluetooth-ap-device"
        log_info "  journalctl -u bluetooth-ap-device -f"
        
    elif [ "$mode" = "foreground" ]; then
        log_info "在前台启动服务..."
        export PYTHONPATH=$(pwd)
        export DEVICE_NAME=${DEVICE_NAME:-RK3588-AP-Device}
        export WIFI_INTERFACE=${WIFI_INTERFACE:-wlan0}
        export AUTO_START_AP=${AUTO_START_AP:-false}
        
        python3 device/main.py
    fi
}

# 显示帮助信息
show_help() {
    echo "RK3588设备端启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -i, --install           安装依赖和配置系统"
    echo "  -s, --start             启动系统服务"
    echo "  -f, --foreground        在前台运行"
    echo "  -r, --restart           重启服务"
    echo "  --stop                  停止服务"
    echo "  --status                查看服务状态"
    echo ""
    echo "环境变量:"
    echo "  DEVICE_NAME             设备名称 (默认: RK3588-AP-Device)"
    echo "  WIFI_INTERFACE          WiFi接口 (默认: wlan0)"
    echo "  AUTO_START_AP           自动启动AP (默认: false)"
    echo ""
    echo "示例:"
    echo "  sudo $0 --install       # 安装依赖和配置"
    echo "  sudo $0 --start         # 启动服务"
    echo "  sudo $0 --foreground    # 前台运行"
}

# 主函数
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        -i|--install)
            check_root
            log_info "开始安装和配置..."
            check_dependencies
            check_network_interface "${WIFI_INTERFACE:-wlan0}"
            install_python_deps
            setup_system_service
            enable_bluetooth
            configure_network
            log_success "安装和配置完成！"
            log_info "使用 '$0 --start' 启动服务"
            ;;
        -s|--start)
            check_root
            start_service "service"
            ;;
        -f|--foreground)
            check_root
            start_service "foreground"
            ;;
        -r|--restart)
            check_root
            log_info "重启服务..."
            systemctl restart bluetooth-ap-device
            log_success "服务已重启"
            ;;
        --stop)
            check_root
            log_info "停止服务..."
            systemctl stop bluetooth-ap-device
            log_success "服务已停止"
            ;;
        --status)
            systemctl status bluetooth-ap-device
            ;;
        "")
            log_error "缺少参数"
            show_help
            exit 1
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
