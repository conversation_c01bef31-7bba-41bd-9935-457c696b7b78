"""
设备端WiFi AP管理器
用于在RK3588设备上创建和管理WiFi热点
"""

import subprocess
import logging
import asyncio
import os
import tempfile
import socket
from typing import Optional, Dict, Any
import sys

# 添加common目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from common.protocol import APInfo

logger = logging.getLogger(__name__)

class WiFiAPManager:
    """WiFi AP管理器"""
    
    def __init__(self, interface: str = "wlan0"):
        self.interface = interface
        self.is_running = False
        self.current_ap_info: Optional[APInfo] = None
        self.hostapd_process: Optional[subprocess.Popen] = None
        self.dnsmasq_process: Optional[subprocess.Popen] = None
        
        # 配置文件路径
        self.hostapd_conf = "/tmp/hostapd.conf"
        self.dnsmasq_conf = "/tmp/dnsmasq.conf"
        
    def _check_interface_exists(self) -> bool:
        """检查网络接口是否存在"""
        try:
            result = subprocess.run(
                ["ip", "link", "show", self.interface],
                capture_output=True,
                text=True,
                check=True
            )
            return True
        except subprocess.CalledProcessError:
            logger.error(f"网络接口 {self.interface} 不存在")
            return False
    
    def _create_hostapd_config(self, ap_info: APInfo) -> str:
        """创建hostapd配置文件"""
        config_content = f"""
interface={self.interface}
driver=nl80211
ssid={ap_info.ssid}
hw_mode=g
channel=7
wmm_enabled=0
macaddr_acl=0
auth_algs=1
ignore_broadcast_ssid=0
wpa=2
wpa_passphrase={ap_info.password}
wpa_key_mgmt=WPA-PSK
wpa_pairwise=TKIP
rsn_pairwise=CCMP
"""
        
        with open(self.hostapd_conf, 'w') as f:
            f.write(config_content.strip())
        
        logger.info(f"创建hostapd配置文件: {self.hostapd_conf}")
        return self.hostapd_conf
    
    def _create_dnsmasq_config(self, ap_info: APInfo) -> str:
        """创建dnsmasq配置文件"""
        # 计算DHCP范围
        ip_parts = ap_info.ip.split('.')
        network_base = '.'.join(ip_parts[:3])
        dhcp_start = f"{network_base}.10"
        dhcp_end = f"{network_base}.50"
        
        config_content = f"""
interface={self.interface}
dhcp-range={dhcp_start},{dhcp_end},*************,24h
dhcp-option=3,{ap_info.ip}
dhcp-option=6,{ap_info.ip}
server=*******
log-queries
log-dhcp
listen-address={ap_info.ip}
"""
        
        with open(self.dnsmasq_conf, 'w') as f:
            f.write(config_content.strip())
        
        logger.info(f"创建dnsmasq配置文件: {self.dnsmasq_conf}")
        return self.dnsmasq_conf
    
    def _configure_interface(self, ap_info: APInfo) -> bool:
        """配置网络接口"""
        try:
            # 停止NetworkManager对该接口的管理
            subprocess.run(
                ["nmcli", "device", "set", self.interface, "managed", "no"],
                check=False  # 如果没有NetworkManager也不报错
            )
            
            # 设置接口IP地址
            subprocess.run(
                ["ip", "addr", "flush", "dev", self.interface],
                check=True
            )
            
            subprocess.run(
                ["ip", "addr", "add", f"{ap_info.ip}/24", "dev", self.interface],
                check=True
            )
            
            # 启用接口
            subprocess.run(
                ["ip", "link", "set", "dev", self.interface, "up"],
                check=True
            )
            
            logger.info(f"网络接口 {self.interface} 配置完成，IP: {ap_info.ip}")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"配置网络接口失败: {e}")
            return False
    
    def _start_hostapd(self) -> bool:
        """启动hostapd"""
        try:
            # 检查hostapd是否已安装
            subprocess.run(["which", "hostapd"], check=True, capture_output=True)
            
            # 启动hostapd
            self.hostapd_process = subprocess.Popen(
                ["hostapd", self.hostapd_conf],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待一下确保启动成功
            asyncio.sleep(2)
            
            if self.hostapd_process.poll() is None:
                logger.info("hostapd启动成功")
                return True
            else:
                logger.error("hostapd启动失败")
                return False
                
        except subprocess.CalledProcessError:
            logger.error("hostapd未安装，请安装: sudo apt-get install hostapd")
            return False
        except Exception as e:
            logger.error(f"启动hostapd失败: {e}")
            return False
    
    def _start_dnsmasq(self) -> bool:
        """启动dnsmasq"""
        try:
            # 检查dnsmasq是否已安装
            subprocess.run(["which", "dnsmasq"], check=True, capture_output=True)
            
            # 停止系统的dnsmasq服务（如果在运行）
            subprocess.run(["systemctl", "stop", "dnsmasq"], check=False)
            
            # 启动dnsmasq
            self.dnsmasq_process = subprocess.Popen(
                ["dnsmasq", "-C", self.dnsmasq_conf, "-d"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待一下确保启动成功
            await asyncio.sleep(2)
            
            if self.dnsmasq_process.poll() is None:
                logger.info("dnsmasq启动成功")
                return True
            else:
                logger.error("dnsmasq启动失败")
                return False
                
        except subprocess.CalledProcessError:
            logger.error("dnsmasq未安装，请安装: sudo apt-get install dnsmasq")
            return False
        except Exception as e:
            logger.error(f"启动dnsmasq失败: {e}")
            return False
    
    def _enable_ip_forwarding(self) -> bool:
        """启用IP转发"""
        try:
            with open("/proc/sys/net/ipv4/ip_forward", "w") as f:
                f.write("1")
            
            # 设置iptables规则（如果需要网络共享）
            subprocess.run([
                "iptables", "-t", "nat", "-A", "POSTROUTING", 
                "-o", "eth0", "-j", "MASQUERADE"
            ], check=False)
            
            subprocess.run([
                "iptables", "-A", "FORWARD", "-i", "eth0", 
                "-o", self.interface, "-m", "state", 
                "--state", "RELATED,ESTABLISHED", "-j", "ACCEPT"
            ], check=False)
            
            subprocess.run([
                "iptables", "-A", "FORWARD", "-i", self.interface, 
                "-o", "eth0", "-j", "ACCEPT"
            ], check=False)
            
            logger.info("IP转发已启用")
            return True
            
        except Exception as e:
            logger.error(f"启用IP转发失败: {e}")
            return False
    
    async def start_ap(self, ssid: str, password: str, ip: str = "***********") -> bool:
        """启动WiFi AP"""
        if self.is_running:
            logger.warning("WiFi AP已在运行")
            return True
        
        if not self._check_interface_exists():
            return False
        
        # 创建AP信息
        ap_info = APInfo(ssid, password, ip)
        
        try:
            # 1. 配置网络接口
            if not self._configure_interface(ap_info):
                return False
            
            # 2. 创建配置文件
            self._create_hostapd_config(ap_info)
            self._create_dnsmasq_config(ap_info)
            
            # 3. 启动hostapd
            if not self._start_hostapd():
                return False
            
            # 4. 启动dnsmasq
            if not await self._start_dnsmasq():
                await self.stop_ap()
                return False
            
            # 5. 启用IP转发
            self._enable_ip_forwarding()
            
            self.is_running = True
            self.current_ap_info = ap_info
            
            logger.info(f"WiFi AP启动成功: SSID={ssid}, IP={ip}")
            return True
            
        except Exception as e:
            logger.error(f"启动WiFi AP失败: {e}")
            await self.stop_ap()
            return False
    
    async def stop_ap(self) -> bool:
        """停止WiFi AP"""
        if not self.is_running:
            logger.info("WiFi AP未运行")
            return True
        
        try:
            # 停止进程
            if self.dnsmasq_process:
                self.dnsmasq_process.terminate()
                self.dnsmasq_process.wait(timeout=5)
                self.dnsmasq_process = None
            
            if self.hostapd_process:
                self.hostapd_process.terminate()
                self.hostapd_process.wait(timeout=5)
                self.hostapd_process = None
            
            # 清理网络配置
            subprocess.run(
                ["ip", "addr", "flush", "dev", self.interface],
                check=False
            )
            
            # 恢复NetworkManager管理
            subprocess.run(
                ["nmcli", "device", "set", self.interface, "managed", "yes"],
                check=False
            )
            
            # 清理配置文件
            for conf_file in [self.hostapd_conf, self.dnsmasq_conf]:
                if os.path.exists(conf_file):
                    os.remove(conf_file)
            
            self.is_running = False
            self.current_ap_info = None
            
            logger.info("WiFi AP已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止WiFi AP失败: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取AP状态"""
        return {
            "running": self.is_running,
            "interface": self.interface,
            "ap_info": self.current_ap_info.to_dict() if self.current_ap_info else None
        }
    
    def get_connected_clients(self) -> list:
        """获取连接的客户端列表"""
        clients = []
        try:
            # 读取DHCP租约文件
            lease_file = "/var/lib/dhcp/dhcpd.leases"
            if os.path.exists(lease_file):
                with open(lease_file, 'r') as f:
                    content = f.read()
                    # 解析租约信息（简化版本）
                    # 实际实现需要更复杂的解析逻辑
                    pass
        except Exception as e:
            logger.error(f"获取客户端列表失败: {e}")
        
        return clients

# 测试代码
async def test_wifi_manager():
    """测试WiFi管理器"""
    logging.basicConfig(level=logging.INFO)
    
    manager = WiFiAPManager("wlan0")
    
    try:
        print("启动WiFi AP...")
        success = await manager.start_ap("TestAP", "12345678", "***********")
        
        if success:
            print("WiFi AP启动成功！")
            print("状态:", manager.get_status())
            
            # 运行一段时间
            await asyncio.sleep(30)
        else:
            print("WiFi AP启动失败")
            
    except KeyboardInterrupt:
        print("正在停止...")
    finally:
        await manager.stop_ap()

if __name__ == "__main__":
    asyncio.run(test_wifi_manager())
