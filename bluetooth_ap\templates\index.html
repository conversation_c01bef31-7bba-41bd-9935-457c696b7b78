<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蓝牙AP控制系统</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .status-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #007bff;
        }
        .status-card.connected {
            border-left-color: #28a745;
        }
        .status-card.disconnected {
            border-left-color: #dc3545;
        }
        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .control-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }
        .control-section h3 {
            margin-top: 0;
            color: #495057;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        .btn.warning:hover {
            background: #e0a800;
        }
        .log-panel {
            background: #212529;
            color: #fff;
            border-radius: 8px;
            padding: 20px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-entry.error {
            color: #ff6b6b;
        }
        .log-entry.warning {
            color: #ffd93d;
        }
        .log-entry.info {
            color: #74c0fc;
        }
        .device-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 10px;
        }
        .device-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .device-item:hover {
            background: #f8f9fa;
        }
        .device-item:last-child {
            border-bottom: none;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.connected {
            background: #28a745;
        }
        .status-indicator.disconnected {
            background: #dc3545;
        }
        .ap-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }
        .ap-info h4 {
            margin-top: 0;
            color: #0066cc;
        }
        .ap-info-item {
            margin-bottom: 8px;
        }
        .ap-info-item strong {
            display: inline-block;
            width: 60px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 蓝牙AP控制系统</h1>
            <p>通过蓝牙控制设备创建WiFi热点并自动连接</p>
        </div>

        <!-- 状态面板 -->
        <div class="status-panel">
            <div class="status-card" id="bluetooth-status">
                <h3>🔵 蓝牙连接状态</h3>
                <p><span class="status-indicator disconnected"></span><span id="bt-status-text">未连接</span></p>
                <p id="bt-device-info">设备: 无</p>
            </div>
            <div class="status-card" id="device-status">
                <h3>📡 设备AP状态</h3>
                <p><span class="status-indicator disconnected"></span><span id="ap-status-text">未运行</span></p>
                <p id="ap-last-update">最后更新: 无</p>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <!-- 蓝牙控制 -->
            <div class="control-section">
                <h3>🔍 蓝牙设备管理</h3>
                <button class="btn" onclick="scanDevices()">扫描设备</button>
                <button class="btn danger" onclick="disconnectDevice()">断开连接</button>
                
                <div class="device-list" id="device-list" style="display: none;">
                    <!-- 设备列表将在这里显示 -->
                </div>
            </div>

            <!-- AP控制 -->
            <div class="control-section">
                <h3>📶 AP热点控制</h3>
                <div class="form-group">
                    <label for="ap-ssid">AP名称 (可选):</label>
                    <input type="text" id="ap-ssid" placeholder="留空使用默认名称">
                </div>
                <div class="form-group">
                    <label for="ap-password">AP密码 (可选):</label>
                    <input type="password" id="ap-password" placeholder="留空使用默认密码">
                </div>
                <button class="btn success" onclick="startAP()">启动AP</button>
                <button class="btn danger" onclick="stopAP()">停止AP</button>
                
                <div id="ap-info" class="ap-info" style="display: none;">
                    <h4>📋 AP信息</h4>
                    <div class="ap-info-item"><strong>SSID:</strong> <span id="ap-info-ssid">-</span></div>
                    <div class="ap-info-item"><strong>密码:</strong> <span id="ap-info-password">-</span></div>
                    <div class="ap-info-item"><strong>IP:</strong> <span id="ap-info-ip">-</span></div>
                </div>
            </div>

            <!-- WiFi控制 -->
            <div class="control-section">
                <h3>📡 WiFi连接管理</h3>
                <button class="btn warning" onclick="connectWiFi()">连接到设备AP</button>
                <button class="btn danger" onclick="disconnectWiFi()">断开WiFi</button>
                <p><small>注意: 连接到设备AP后可能会断开当前网络</small></p>
            </div>
        </div>

        <!-- 日志面板 -->
        <div class="log-panel" id="log-panel">
            <div class="log-entry info">系统已启动，等待操作...</div>
        </div>
    </div>

    <script>
        // Socket.IO连接
        const socket = io();
        
        // 全局状态
        let deviceList = [];
        let currentStatus = {
            bluetooth: { connected: false },
            device: { connected: false, ap_running: false }
        };

        // Socket事件处理
        socket.on('connect', function() {
            addLog('已连接到服务器', 'info');
        });

        socket.on('disconnect', function() {
            addLog('与服务器断开连接', 'warning');
        });

        socket.on('status_update', function(data) {
            updateStatus(data);
        });

        socket.on('bluetooth_status', function(data) {
            updateBluetoothStatus(data);
        });

        socket.on('devices_found', function(data) {
            displayDevices(data.devices);
        });

        socket.on('log', function(data) {
            addLog(data.message, data.type || 'info');
        });

        // 更新状态显示
        function updateStatus(data) {
            currentStatus = data;
            
            // 更新蓝牙状态
            if (data.bluetooth) {
                updateBluetoothStatus(data.bluetooth);
            }
            
            // 更新设备状态
            if (data.device) {
                updateDeviceStatus(data.device);
            }
        }

        function updateBluetoothStatus(status) {
            const statusCard = document.getElementById('bluetooth-status');
            const statusText = document.getElementById('bt-status-text');
            const deviceInfo = document.getElementById('bt-device-info');
            const indicator = statusCard.querySelector('.status-indicator');
            
            if (status.connected) {
                statusCard.className = 'status-card connected';
                statusText.textContent = '已连接';
                deviceInfo.textContent = `设备: ${status.device_name || status.device_address}`;
                indicator.className = 'status-indicator connected';
            } else {
                statusCard.className = 'status-card disconnected';
                statusText.textContent = '未连接';
                deviceInfo.textContent = '设备: 无';
                indicator.className = 'status-indicator disconnected';
            }
        }

        function updateDeviceStatus(status) {
            const statusCard = document.getElementById('device-status');
            const statusText = document.getElementById('ap-status-text');
            const lastUpdate = document.getElementById('ap-last-update');
            const indicator = statusCard.querySelector('.status-indicator');
            
            if (status.ap_running) {
                statusCard.className = 'status-card connected';
                statusText.textContent = 'AP运行中';
                indicator.className = 'status-indicator connected';
            } else {
                statusCard.className = 'status-card disconnected';
                statusText.textContent = 'AP未运行';
                indicator.className = 'status-indicator disconnected';
            }
            
            if (status.last_update) {
                const date = new Date(status.last_update);
                lastUpdate.textContent = `最后更新: ${date.toLocaleString()}`;
            }
            
            // 更新AP信息
            if (status.ap_info) {
                displayAPInfo(status.ap_info);
            } else {
                hideAPInfo();
            }
        }

        function displayAPInfo(apInfo) {
            const apInfoDiv = document.getElementById('ap-info');
            document.getElementById('ap-info-ssid').textContent = apInfo.ssid;
            document.getElementById('ap-info-password').textContent = apInfo.password;
            document.getElementById('ap-info-ip').textContent = apInfo.ip;
            apInfoDiv.style.display = 'block';
        }

        function hideAPInfo() {
            document.getElementById('ap-info').style.display = 'none';
        }

        function displayDevices(devices) {
            deviceList = devices;
            const deviceListDiv = document.getElementById('device-list');
            
            if (devices.length === 0) {
                deviceListDiv.innerHTML = '<div class="device-item">未发现设备</div>';
            } else {
                deviceListDiv.innerHTML = devices.map((device, index) => 
                    `<div class="device-item" onclick="connectDevice('${device.address}')">
                        <div>
                            <strong>${device.name}</strong><br>
                            <small>${device.address}</small>
                        </div>
                        <button class="btn" onclick="event.stopPropagation(); connectDevice('${device.address}')">连接</button>
                    </div>`
                ).join('');
            }
            
            deviceListDiv.style.display = 'block';
        }

        function addLog(message, type = 'info') {
            const logPanel = document.getElementById('log-panel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
            
            // 限制日志条数
            const entries = logPanel.querySelectorAll('.log-entry');
            if (entries.length > 100) {
                entries[0].remove();
            }
        }

        // 控制函数
        function scanDevices() {
            fetch('/api/scan', { method: 'POST' })
                .then(response => response.json())
                .then(data => addLog(data.message))
                .catch(error => addLog('扫描请求失败: ' + error, 'error'));
        }

        function connectDevice(address) {
            fetch('/api/connect', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ address: address })
            })
            .then(response => response.json())
            .then(data => addLog(data.message))
            .catch(error => addLog('连接请求失败: ' + error, 'error'));
        }

        function disconnectDevice() {
            fetch('/api/disconnect', { method: 'POST' })
                .then(response => response.json())
                .then(data => addLog(data.message))
                .catch(error => addLog('断开请求失败: ' + error, 'error'));
        }

        function startAP() {
            const ssid = document.getElementById('ap-ssid').value;
            const password = document.getElementById('ap-password').value;
            
            fetch('/api/control', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    action: 'start',
                    ssid: ssid,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => addLog(data.message))
            .catch(error => addLog('启动AP请求失败: ' + error, 'error'));
        }

        function stopAP() {
            fetch('/api/control', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'stop' })
            })
            .then(response => response.json())
            .then(data => addLog(data.message))
            .catch(error => addLog('停止AP请求失败: ' + error, 'error'));
        }

        function connectWiFi() {
            fetch('/api/wifi/connect', { method: 'POST' })
                .then(response => response.json())
                .then(data => addLog(data.message))
                .catch(error => addLog('WiFi连接请求失败: ' + error, 'error'));
        }

        function disconnectWiFi() {
            fetch('/api/wifi/disconnect', { method: 'POST' })
                .then(response => response.json())
                .then(data => addLog(data.message))
                .catch(error => addLog('WiFi断开请求失败: ' + error, 'error'));
        }

        // 页面加载完成后获取初始状态
        window.addEventListener('load', function() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => updateStatus(data))
                .catch(error => addLog('获取状态失败: ' + error, 'error'));
        });
    </script>
</body>
</html>
