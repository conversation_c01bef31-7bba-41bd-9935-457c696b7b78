# 🔗 蓝牙AP控制系统

通过蓝牙BLE协议控制RK3588设备创建WiFi热点，并实现电脑端自动连接的完整解决方案。

## 📋 系统概述

本系统实现了电脑通过蓝牙扫描并连接到RK3588设备，发送控制命令让设备创建WiFi AP热点，然后电脑自动连接到该热点的完整流程。

### 🎯 核心功能

- **🔍 设备发现**: 自动扫描并识别目标RK3588设备
- **📡 蓝牙通信**: 基于BLE协议的可靠命令传输
- **📶 AP管理**: 动态创建和管理WiFi热点
- **🔄 自动连接**: 智能WiFi连接和状态监控
- **🌐 Web界面**: 直观的图形化控制界面
- **💻 命令行**: 灵活的CLI控制工具

## 🏗️ 系统架构

```
┌─────────────────┐    蓝牙BLE     ┌─────────────────┐
│   电脑端        │ ◄──────────► │   RK3588设备    │
│                 │              │                 │
│ • BLE客户端     │   控制命令    │ • BLE服务器     │
│ • WiFi管理器    │   AP信息      │ • WiFi AP管理   │
│ • Web界面       │              │ • 系统服务      │
│ • CLI工具       │              │                 │
└─────────────────┘              └─────────────────┘
         │                                │
         │          WiFi连接              │
         └────────────────────────────────┘
```

### 设备端 (RK3588 Ubuntu 20.04)
- **BLE服务器**: 接收和处理控制命令
- **WiFi AP管理**: 使用hostapd和dnsmasq创建热点
- **系统集成**: systemd服务和自动启动
- **日志记录**: 完整的操作日志和错误处理

### 电脑端 (Windows/Linux)
- **BLE客户端**: 设备扫描和连接管理
- **WiFi管理**: 跨平台WiFi连接支持
- **Web界面**: 基于Flask的现代化控制面板
- **CLI工具**: 命令行自动化工具

## 🚀 快速开始

### 1. 环境要求

**设备端 (RK3588)**:
- Ubuntu 20.04 LTS
- Python 3.7+
- 蓝牙和WiFi硬件支持
- root权限

**电脑端**:
- Windows 10+ 或 Linux
- Python 3.7+
- 蓝牙适配器
- WiFi适配器

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd bluetooth_ap

# 安装Python依赖
pip install -r requirements.txt
```

### 3. 设备端部署

在RK3588设备上执行：

```bash
# 安装系统依赖和配置服务
sudo ./start_device.sh --install

# 启动服务
sudo ./start_device.sh --start

# 查看服务状态
sudo ./start_device.sh --status
```

### 4. 电脑端使用

#### Web界面模式 (推荐)
```bash
python start_computer.py web
```
然后在浏览器中访问 `http://localhost:5000`

#### 命令行模式
```bash
python start_computer.py cli
```

#### 自动模式
```bash
python start_computer.py auto --ssid "MyAP" --password "12345678"
```

## 📁 项目结构

```
bluetooth_ap/
├── device/                 # 设备端代码
│   ├── main.py            # 设备端主程序
│   ├── ble_server.py      # BLE服务器实现
│   └── wifi_manager.py    # WiFi AP管理器
├── computer/              # 电脑端代码
│   ├── main.py            # 电脑端CLI工具
│   ├── ble_client.py      # BLE客户端实现
│   └── wifi_manager.py    # WiFi连接管理器
├── common/                # 共享代码
│   └── protocol.py        # 通信协议定义
├── templates/             # Web界面模板
│   └── index.html         # 主界面
├── web_app.py             # Web应用服务器
├── start_device.sh        # 设备端启动脚本
├── start_computer.py      # 电脑端启动脚本
├── requirements.txt       # Python依赖
└── README.md              # 项目文档
```