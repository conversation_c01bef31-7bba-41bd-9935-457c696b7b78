"""
蓝牙通信协议定义
"""

import uuid
from enum import Enum
from typing import Dict, Any
import json

# 服务UUID
AP_CONTROL_SERVICE_UUID = "12345678-1234-1234-1234-123456789abc"

# 特征UUID
COMMAND_CHARACTERISTIC_UUID = "*************-4321-4321-cba987654321"
STATUS_CHARACTERISTIC_UUID = "11111111-**************-************"
AP_INFO_CHARACTERISTIC_UUID = "aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee"

class CommandType(Enum):
    """命令类型"""
    START_AP = "start_ap"
    STOP_AP = "stop_ap"
    GET_STATUS = "get_status"
    GET_AP_INFO = "get_ap_info"

class StatusType(Enum):
    """状态类型"""
    AP_RUNNING = "ap_running"
    AP_STOPPED = "ap_stopped"
    ERROR = "error"
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"

class APInfo:
    """AP信息"""
    def __init__(self, ssid: str, password: str, ip: str = "***********"):
        self.ssid = ssid
        self.password = password
        self.ip = ip
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "ssid": self.ssid,
            "password": self.password,
            "ip": self.ip
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'APInfo':
        return cls(
            ssid=data["ssid"],
            password=data["password"],
            ip=data.get("ip", "***********")
        )

class Protocol:
    """通信协议"""
    
    @staticmethod
    def encode_command(cmd_type: CommandType, **kwargs) -> bytes:
        """编码命令"""
        data = {
            "type": cmd_type.value,
            "params": kwargs
        }
        return json.dumps(data).encode('utf-8')
    
    @staticmethod
    def decode_command(data: bytes) -> tuple[CommandType, dict]:
        """解码命令"""
        json_data = json.loads(data.decode('utf-8'))
        cmd_type = CommandType(json_data["type"])
        params = json_data.get("params", {})
        return cmd_type, params
    
    @staticmethod
    def encode_status(status: StatusType, message: str = "") -> bytes:
        """编码状态"""
        data = {
            "status": status.value,
            "message": message
        }
        return json.dumps(data).encode('utf-8')
    
    @staticmethod
    def decode_status(data: bytes) -> tuple[StatusType, str]:
        """解码状态"""
        json_data = json.loads(data.decode('utf-8'))
        status = StatusType(json_data["status"])
        message = json_data.get("message", "")
        return status, message
    
    @staticmethod
    def encode_ap_info(ap_info: APInfo) -> bytes:
        """编码AP信息"""
        return json.dumps(ap_info.to_dict()).encode('utf-8')
    
    @staticmethod
    def decode_ap_info(data: bytes) -> APInfo:
        """解码AP信息"""
        json_data = json.loads(data.decode('utf-8'))
        return APInfo.from_dict(json_data) 